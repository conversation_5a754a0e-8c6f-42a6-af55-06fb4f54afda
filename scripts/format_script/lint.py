#!/usr/bin/env python3
"""代码质量检查脚本，集成 flake8 和 pylint"""

import subprocess
import sys
from pathlib import Path
from typing import List, Tu<PERSON>


def run_flake8(paths: List[str]) -> Tuple[int, str]:
    """运行 flake8 检查

    Args:
        paths: 要检查的路径列表

    Returns:
        (返回码, 输出信息)
    """
    try:
        result = subprocess.run(
            ["flake8", *paths],
            capture_output=True,
            text=True,  # 使用文本模式，自动处理编码
            check=False,
        )
        return result.returncode, result.stdout
    except FileNotFoundError:
        return 1, "Error: flake8 not found. Please install it with: pip install flake8"
    except Exception as e:
        return 1, f"Error running flake8: {str(e)}"


def run_pylint(paths: List[str]) -> Tuple[int, str]:
    """运行 pylint 检查

    Args:
        paths: 要检查的路径列表

    Returns:
        (返回码, 输出信息)
    """
    try:
        result = subprocess.run(
            ["pylint", *paths],
            capture_output=True,
            text=True,
            check=False,
        )
        return result.returncode, result.stdout
    except FileNotFoundError:
        return 1, "Error: pylint not found. Please install it with: pip install pylint"
    except Exception as e:
        return 1, f"Error running pylint: {str(e)}"


def main():
    """主函数"""
    # 默认检查 src 目录
    paths = sys.argv[1:] or ["src"]  # 如果没有提供路径，默认检查 src 目录

    # 确保路径存在
    for path in paths:
        if not Path(path).exists():
            print(f"Error: Path does not exist: {path}")
            sys.exit(1)

    # 运行 flake8
    print("Running flake8...")
    flake8_code, flake8_output = run_flake8(paths)
    if flake8_output:
        print(flake8_output)

    # 运行 pylint
    print("\nRunning pylint...")
    pylint_code, pylint_output = run_pylint(paths)
    if pylint_output:
        print(pylint_output)

    # 如果任一工具失败则返回非零值
    sys.exit(flake8_code or pylint_code)


if __name__ == "__main__":
    main()
