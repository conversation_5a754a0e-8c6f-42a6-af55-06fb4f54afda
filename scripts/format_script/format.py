#!/usr/bin/env python3
"""代码格式化脚本，集成 black 和 isort"""

import subprocess
import sys
from pathlib import Path
from typing import List, <PERSON><PERSON>


def run_black(paths: List[str]) -> Tuple[int, str]:
    """运行 black 格式化

    Args:
        paths: 要格式化的路径列表

    Returns:
        (返回码, 输出信息)
    """
    try:
        result = subprocess.run(
            ["black", *paths],
            capture_output=True,
            text=True,
            check=False,
        )
        return result.returncode, result.stdout
    except FileNotFoundError:
        return 1, "Error: black not found. Please install it with: pip install black"


def run_isort(paths: List[str]) -> Tuple[int, str]:
    """运行 isort 导入排序

    Args:
        paths: 要排序的路径列表

    Returns:
        (返回码, 输出信息)
    """
    try:
        result = subprocess.run(
            ["isort", *paths],
            capture_output=True,
            text=True,
            check=False,
        )
        return result.returncode, result.stdout
    except FileNotFoundError:
        return 1, "Error: isort not found. Please install it with: pip install isort"


def main():
    """主函数"""
    # 默认格式化 src 目录
    paths = sys.argv[1:]

    # 确保路径存在
    for path in paths:
        if not Path(path).exists():
            print(f"Error: Path does not exist: {path}")
            sys.exit(1)

    # 运行 black
    print("Running black...")
    black_code, black_output = run_black(paths)
    if black_output:
        print(black_output)

    # 运行 isort
    print("\nRunning isort...")
    isort_code, isort_output = run_isort(paths)
    if isort_output:
        print(isort_output)

    # 如果任一工具失败则返回非零值
    sys.exit(black_code or isort_code)


if __name__ == "__main__":
    main()
