#!/usr/bin/env python3
"""类型检查脚本，使用 mypy"""

import subprocess
import sys
from pathlib import Path
from typing import List, <PERSON><PERSON>


def run_mypy(paths: List[str]) -> Tuple[int, str]:
    """运行 mypy 类型检查

    Args:
        paths: 要检查的路径列表

    Returns:
        (返回码, 输出信息)
    """
    try:
        result = subprocess.run(
            ["mypy", "--strict", *paths],
            capture_output=True,
            text=True,
            check=False,
        )
        return result.returncode, result.stdout
    except FileNotFoundError:
        msg = "Error: mypy not found. Please install it with: pip install mypy"
        return 1, msg


def main():
    """主函数"""
    # 默认检查 src 目录
    paths = sys.argv[1:]

    # 确保路径存在
    for path in paths:
        if not Path(path).exists():
            print(f"Error: Path does not exist: {path}")
            sys.exit(1)

    # 运行 mypy
    print("Running mypy with --strict...")
    mypy_code, mypy_output = run_mypy(paths)
    if mypy_output:
        print(mypy_output)

    sys.exit(mypy_code)


if __name__ == "__main__":
    main()
