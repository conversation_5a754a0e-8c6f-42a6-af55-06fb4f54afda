"""
测试配置文件

提供测试所需的 fixtures 和配置
"""

import pytest
import asyncio
import tempfile
import json
import os
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from domain_common.models import CommonStatus
from domain_common.models.iam_models import Role, Permission, User, Tenant, AuditLog, RolePermission, UserRole
from main import app
from container import ServiceContainer
from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.storages.connect_manager import ConnectionManager
from commonlib.core.containers.config_container import ConfigContainer, set_up_config_di
from commonlib.core.containers.infra_container import InfraContainer
from commonlib.configs.base_setting import AppSettings
from commonlib.configs.config_loader import ConfigLoader
from services import TenantService
from security.jwt_manager import JWTManager
from security.session_manager import SessionManager
from security.cache_manager import CacheManager
from security.security_utils import SecurityUtils
from domain_common.app_builder.default_app_factory import AppInitializer


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    session = AsyncMock(spec=AsyncSession)
    session.add = MagicMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.flush = AsyncMock()
    session.delete = AsyncMock()
    return session


@pytest.fixture
def mock_redis_repo():
    """模拟 Redis 仓储"""
    mock_repo = AsyncMock(spec=RedisRepository)

    # 设置默认返回值
    mock_repo.set.return_value = None
    mock_repo.get.return_value = None
    mock_repo.delete.return_value = None
    mock_repo.keys.return_value = []
    mock_repo.exists.return_value = False
    mock_repo.expire.return_value = None
    mock_repo.ttl.return_value = -1
    mock_repo.incr.return_value = 1
    mock_repo.decr.return_value = 0
    mock_repo.lpush.return_value = None
    mock_repo.rpush.return_value = None
    mock_repo.lpop.return_value = None
    mock_repo.rpop.return_value = None
    mock_repo.llen.return_value = 0
    mock_repo.lrange.return_value = []
    mock_repo.ltrim.return_value = None
    mock_repo.sadd.return_value = None
    mock_repo.srem.return_value = None
    mock_repo.smembers.return_value = set()
    mock_repo.sismember.return_value = False
    mock_repo.clear_all.return_value = True
    mock_repo.delete_pattern.return_value = True
    mock_repo.ping.return_value = True

    return mock_repo


@pytest.fixture
def mock_tenant_service():
    """模拟租户服务"""
    mock_service = AsyncMock()
    mock_service.create_tenant.return_value = {
        "tenant_id": "test_tenant",
        "tenant_name": "测试租户",
        "status": "pending"
    }
    mock_service.list_tenants.return_value = {
        "items": [],
        "has_more": False,
        "total": 0
    }
    return mock_service


@pytest.fixture
def mock_user_service():
    """模拟用户服务"""
    mock_service = AsyncMock()
    mock_service.create_user.return_value = {
        "user_id": "test_user",
        "username": "testuser",
        "status": "pending"
    }
    mock_service.list_users.return_value = {
        "items": [],
        "has_more": False,
        "total": 0
    }
    return mock_service


@pytest.fixture
def mock_auth_service():
    """模拟认证服务"""
    mock_service = AsyncMock()
    mock_service.login.return_value = {
        "access_token": "test_token",
        "token_type": "Bearer",
        "expires_in": 7200
    }
    mock_service.logout.return_value = {
        "status": "success"
    }
    return mock_service


@pytest.fixture
def sample_user_data():
    """示例用户数据"""
    return {
        "tenant_id": "test_tenant",
        "username": "testuser",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "nickname": "测试用户",
        "password": "Test123456!"
    }


@pytest.fixture
def sample_login_data():
    """示例登录数据"""
    return {
        "tenant_id": "test_tenant",
        "username": "testuser",
        "password": "Test123456!",
        "login_type": "password"
    }


@pytest.fixture
def sample_request_data():
    """示例请求数据"""
    return {
        "meta": {
            "request_id": "test_req_123",
            "timestamp": "2025-01-22 10:30:45.123",
            "version": "v1"
        },
        "data": {}
    }


# JWT测试相关的fixtures
@pytest.fixture
def sample_jwt_config():
    """示例JWT配置"""
    return {
        "algorithm": "RS256",
        "access_token_expire_minutes": 30,
        "refresh_token_expire_days": 7,
        "issuer": "test-iam-service",
        "audience": "test-rag-platform"
    }


@pytest.fixture
def sample_session_config():
    """示例会话配置"""
    return {
        "session_timeout_minutes": 120,
        "max_concurrent_sessions": 5,
        "enable_device_tracking": True,
        "enable_ip_tracking": True,
        "check_device_fingerprint": True,
        "check_ip_change": True
    }


@pytest.fixture
def sample_cache_config():
    """示例缓存配置"""
    return {
        "user_info_ttl": 3600,
        "user_roles_ttl": 1800,
        "user_permissions_ttl": 1800,
        "role_permissions_ttl": 3600,
        "session_ttl": 7200,
        "permission_check_ttl": 300
    }


@pytest.fixture
def sample_session():
    """示例会话数据"""
    return {
        "session_id": "test_session_789",
        "user_id": "test_user_123",
        "tenant_id": "test_tenant_456",
        "device_fingerprint": "test_device_abc123",
        "ip_address": "*************",
        "user_agent": "Mozilla/5.0 (Test Browser)",
        "is_active": True
    }


@pytest.fixture
def sample_device_info():
    """示例设备信息"""
    return {
        "fingerprint": "test_device_abc123",
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "screen_resolution": "1920x1080",
        "timezone": "Asia/Shanghai",
        "language": "zh-CN",
        "platform": "Win32",
        "plugins": ["Chrome PDF Plugin", "Chrome PDF Viewer"],
        "canvas_fingerprint": "canvas_hash_123456"
    }


def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "performance: marks tests as performance tests"
    )
    config.addinivalue_line(
        "markers", "security: marks tests as security tests"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 为集成测试添加慢速标记
    for item in items:
        if "integration" in item.keywords:
            item.add_marker(pytest.mark.slow)

        if "performance" in item.keywords:
            item.add_marker(pytest.mark.slow)


@pytest.fixture(autouse=True)
async def cleanup_test_data():
    """自动清理测试数据"""
    # 测试前的设置
    yield

    # 测试后的清理
    # 这里可以添加清理逻辑，比如清理测试Redis数据等
    pass


# 性能测试相关的fixtures
@pytest.fixture
def performance_config():
    """性能测试配置"""
    return {
        "token_generation_target_rps": 100,  # 每秒生成令牌数目标
        "token_verification_target_rps": 500,  # 每秒验证令牌数目标
        "max_memory_increase_mb": 100,  # 最大内存增长（MB）
        "max_token_size_bytes": 8192,  # 最大令牌大小（字节）
    }


@pytest.fixture
def security_test_vectors():
    """安全测试向量"""
    return {
        "invalid_tokens": [
            "",
            "invalid.token.here",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid",
            "Bearer invalid_token",
            None
        ],
        "malicious_payloads": [
            {"user_id": "../../../etc/passwd"},
            {"user_id": "<script>alert('xss')</script>"},
            {"user_id": "'; DROP TABLE users; --"},
            {"roles": ["admin"] * 10000},  # 大量角色
            {"permissions": ["perm"] * 100000}  # 大量权限
        ],
        "suspicious_ips": [
            ("***********", "********"),  # 内网到内网
            ("***********", "*******"),  # 内网到公网
            ("*******", "*******"),  # 公网到公网
        ],
        "device_fingerprints": [
            "normal_device_123",
            "suspicious_device_456",
            "mobile_device_789",
            "bot_device_000"
        ]
    }


@pytest.fixture
def tenant_service(mock_session, mock_redis_repo):
    """创建租户服务实例"""
    return TenantService(
        session=mock_session,
        redis_repo=mock_redis_repo,
        user_model=User,
        tenant_model=Tenant,
        role_model=Role,
        permission_model=Permission,
        user_role_model=UserRole,
        role_permission_model=RolePermission,
        audit_log_model=AuditLog
    )


@pytest.fixture
def sample_tenant():
    """示例租户对象"""
    tenant = MagicMock(spec=Tenant)
    tenant.tenant_id = "tenant_123"
    tenant.tenant_name = "测试企业"
    tenant.tenant_code = "TEST_CORP"
    tenant.description = "这是一个测试企业"
    tenant.status = CommonStatus.ACTIVE
    tenant.max_users = 1000
    tenant.settings = {
        "session_timeout": 3600,
        "password_policy": {
            "min_length": 8,
            "require_special_char": True
        }
    }
    tenant.created_at = "2025-01-23T10:30:45"
    tenant.updated_at = "2025-01-23T11:00:00"
    tenant.deleted_at = None
    return tenant


@pytest.fixture
def sample_user():
    """示例用户对象"""
    user = MagicMock(spec=User)
    user.user_id = "user_123"
    user.tenant_id = "tenant_123"
    user.username = "testuser"
    user.email = "<EMAIL>"
    user.phone = "13800138000"
    user.nickname = "测试用户"
    user.status = CommonStatus.ACTIVE
    user.created_at = "2025-01-23T10:30:45"
    user.updated_at = "2025-01-23T11:00:00"
    user.deleted_at = None
    return user


@pytest.fixture
def sample_role():
    """示例角色对象"""
    role = MagicMock(spec=Role)
    role.role_id = "role_123"
    role.tenant_id = "tenant_123"
    role.role_name = "管理员"
    role.role_code = "ADMIN"
    role.description = "管理员角色"
    role.level = 2
    role.status = CommonStatus.ACTIVE
    role.created_at = "2025-01-23T10:30:45"
    role.updated_at = "2025-01-23T11:00:00"
    role.deleted_at = None
    return role


@pytest.fixture
def sample_permission():
    """示例权限对象"""
    permission = MagicMock(spec=Permission)
    permission.permission_id = "perm_123"
    permission.tenant_id = "tenant_123"
    permission.permission_name = "用户管理"
    permission.permission_code = "user:manage"
    permission.resource = "user"
    permission.action = "manage"
    permission.level = 2
    permission.status = CommonStatus.ACTIVE
    permission.created_at = "2025-01-23T10:30:45"
    permission.updated_at = "2025-01-23T11:00:00"
    permission.deleted_at = None
    return permission


@pytest.fixture
def sample_tenant_data():
    """示例租户创建数据"""
    return {
        "tenant_name": "测试企业",
        "tenant_code": "TEST_CORP",
        "description": "这是一个测试企业",
        "max_users": 500,
        "settings": {
            "session_timeout": 3600,
            "password_policy": {
                "min_length": 8,
                "require_special_char": True
            }
        }
    }


@pytest.fixture
def sample_admin_user_data():
    """示例管理员用户数据"""
    return {
        "username": "admin",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "password": "Admin123!@#",
        "nickname": "管理员"
    }


@pytest.fixture
def sample_api_request():
    """示例API请求数据"""
    return {
        "meta": {
            "request_id": "req_123",
            "timestamp": "2025-01-23 10:30:45.123",
            "version": "v1"
        },
        "data": {}
    }


@pytest.fixture
def mock_database_success(mock_session):
    """模拟数据库操作成功"""
    mock_session.execute.return_value.scalar_one_or_none.return_value = None
    mock_session.execute.return_value.scalar.return_value = 0
    mock_session.execute.return_value.scalars.return_value.all.return_value = []
    return mock_session


@pytest.fixture
def mock_database_with_tenant(mock_session, sample_tenant):
    """模拟数据库包含租户数据"""
    mock_session.execute.return_value.scalar_one_or_none.return_value = sample_tenant
    mock_session.execute.return_value.scalar.return_value = 5  # 用户数量
    mock_session.execute.return_value.scalars.return_value.all.return_value = [sample_tenant]
    return mock_session


@pytest.fixture
def mock_redis_with_cache(mock_redis_repo):
    """模拟Redis包含缓存数据"""
    cached_data = {
        "tenant_id": "tenant_123",
        "tenant_name": "测试企业",
        "tenant_code": "TEST_CORP",
        "status": CommonStatus.ACTIVE
    }
    mock_redis_repo.get.return_value = cached_data
    return mock_redis_repo


# 异步测试支持
@pytest.fixture
def async_test():
    """异步测试支持"""

    def _async_test(coro):
        return asyncio.get_event_loop().run_until_complete(coro)

    return _async_test


# 错误模拟
@pytest.fixture
def mock_database_error(mock_session):
    """模拟数据库错误"""
    from sqlalchemy.exc import IntegrityError, OperationalError

    def set_error(error_type="integrity"):
        if error_type == "integrity":
            mock_session.commit.side_effect = IntegrityError("statement", "params", "orig")
        elif error_type == "operational":
            mock_session.execute.side_effect = OperationalError("statement", "params", "orig")
        elif error_type == "timeout":
            mock_session.execute.side_effect = OperationalError("statement", "params", "timeout")

    mock_session.set_error = set_error
    return mock_session


@pytest.fixture
def mock_redis_error(mock_redis_repo):
    """模拟Redis错误"""

    def set_error(error_type="connection"):
        if error_type == "connection":
            mock_redis_repo.get.side_effect = ConnectionError("Redis connection failed")
            mock_redis_repo.set.side_effect = ConnectionError("Redis connection failed")
            mock_redis_repo.delete.side_effect = ConnectionError("Redis connection failed")
        elif error_type == "timeout":
            mock_redis_repo.get.side_effect = TimeoutError("Redis timeout")
            mock_redis_repo.set.side_effect = TimeoutError("Redis timeout")

    mock_redis_repo.set_error = set_error
    return mock_redis_repo


# ==================== CONFIG FIXTURES ====================


@pytest.fixture
def test_app_settings():
    """测试应用设置"""
    return AppSettings(
        app_name="test_iam_service",
        debug=True,
    )


@pytest.fixture
def mock_config_loader():
    """Mock配置加载器"""
    loader = MagicMock(spec=ConfigLoader)

    # Mock配置对象
    mock_config = MagicMock()
    mock_config.application.project_name = "test_iam_service"
    mock_config.debug = True
    mock_config.log_dir = "/tmp/test_logs"

    # Mock persistence配置
    mock_config.persistence = MagicMock()
    mock_config.persistence.redis = MagicMock()
    mock_config.persistence.mysql = MagicMock()
    mock_config.persistence.postgres = MagicMock()

    # Mock connection_priority配置
    mock_config.connection_priority = MagicMock()

    loader.get_config.return_value = mock_config
    loader.load = MagicMock()

    return loader


@pytest.fixture
def config_environment_variables():
    """配置环境变量"""
    env_vars = {
        "CONFIG_FILE_PATH": "/tmp/test_config.json",
        "DEBUG": "true",
        "LOG_LEVEL": "DEBUG",
        "REDIS_HOST": "localhost",
        "REDIS_PORT": "6379",
        "REDIS_DB": "15",
        "POSTGRES_HOST": "localhost",
        "POSTGRES_PORT": "5432",
        "POSTGRES_DB": "test_iam"
    }

    # 设置环境变量
    original_env = {}
    for key, value in env_vars.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value

    yield env_vars

    # 恢复原始环境变量
    for key, original_value in original_env.items():
        if original_value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = original_value


@pytest.fixture
def mock_dotenv_load():
    """Mock dotenv加载"""
    with patch('commonlib.core.containers.config_container.load_dotenv') as mock_load:
        yield mock_load


@pytest.fixture
def mock_logger_initialize():
    """Mock日志初始化"""
    with patch('commonlib.core.logging.tsif_logging.app_logger.initialize') as mock_init:
        yield mock_init


# ==================== CONTAINER FIXTURES ====================

@pytest.fixture
def mock_config_container(test_app_settings, test_config_file):
    """Mock的配置容器"""
    with patch('commonlib.core.containers.config_container.load_dotenv'):
        container = ConfigContainer()

        # Mock配置加载器
        mock_loader = MagicMock()
        mock_config = MagicMock()
        mock_config.application.project_name = "test_iam_service"
        mock_config.debug = True
        mock_config.log_dir = "/tmp/test_logs"
        mock_config.persistence = MagicMock()
        mock_config.connection_priority = MagicMock()

        mock_loader.get_config.return_value = mock_config
        container.config_loader.override(mock_loader)

        # Mock日志初始化
        with patch('commonlib.core.logging.tsif_logging.app_logger.initialize'):
            container.init_resources()

        return container


@pytest.fixture
def mock_infra_container(mock_config_container):
    """Mock的基础设施容器"""
    container = InfraContainer(config=mock_config_container)

    # Mock连接管理器
    mock_connection_manager = MagicMock()
    mock_redis_client = AsyncMock()
    mock_postgres_client = AsyncMock()
    mock_mysql_client = AsyncMock()

    mock_connection_manager.get_connector.side_effect = lambda name: {
        'redis': mock_redis_client,
        'postgres': mock_postgres_client,
        'mysql': mock_mysql_client
    }.get(name)

    container.connection_manager.override(mock_connection_manager)

    return container


@pytest.fixture
def mock_service_container(mock_config_container, mock_infra_container):
    """Mock的服务容器"""
    container = ServiceContainer(
        config=mock_config_container,
        infra=mock_infra_container
    )

    # Mock数据库会话
    mock_session = AsyncMock()
    container.session.override(mock_session)

    # Mock Redis仓库
    mock_redis_repo = AsyncMock()
    container.redis_repo.override(mock_redis_repo)

    return container


@pytest.fixture
def isolated_config_container():
    """隔离的配置容器（不依赖外部资源）"""
    container = ConfigContainer()

    # 完全Mock所有外部依赖
    with patch('commonlib.core.containers.config_container.load_dotenv'), \
            patch('commonlib.core.logging.tsif_logging.app_logger.initialize'):
        # Mock配置加载器
        mock_loader = MagicMock()
        mock_config = MagicMock()
        mock_config.application.project_name = "isolated_test"
        mock_config.debug = True
        mock_config.log_dir = "/tmp/isolated_logs"

        mock_loader.get_config.return_value = mock_config
        container.config_loader.override(mock_loader)

        yield container


@pytest.fixture
def container_test_environment():
    """容器测试环境配置"""
    return {
        "test_mode": True,
        "mock_external_services": True,
        "use_in_memory_db": True,
        "disable_logging": True,
        "fast_startup": True
    }


@pytest.fixture
def mock_security_components():
    """Mock安全组件"""
    return {
        "jwt_manager": MagicMock(),
        "session_manager": AsyncMock(),
        "cache_manager": AsyncMock(),
        "security_utils": MagicMock()
    }


@pytest.fixture
def mock_external_services():
    """Mock外部服务"""
    return {
        "email_service": AsyncMock(),
        "sms_service": AsyncMock(),
        "verification_service": AsyncMock()
    }


# ==================== MOCK FIXTURES ====================

@pytest.fixture
def mock_async_session():
    """Mock异步数据库会话"""
    session = AsyncMock(spec=AsyncSession)
    session.add = MagicMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.flush = AsyncMock()
    session.delete = AsyncMock()
    session.execute = AsyncMock()
    session.scalar = AsyncMock()
    session.scalars = AsyncMock()
    session.close = AsyncMock()
    return session


@pytest.fixture
def mock_redis_repository():
    """Mock Redis仓库"""
    repo = AsyncMock(spec=RedisRepository)
    repo.get = AsyncMock()
    repo.set = AsyncMock()
    repo.delete = AsyncMock()
    repo.exists = AsyncMock()
    repo.expire = AsyncMock()
    repo.hget = AsyncMock()
    repo.hset = AsyncMock()
    repo.hdel = AsyncMock()
    repo.hgetall = AsyncMock()
    repo.lpush = AsyncMock()
    repo.rpop = AsyncMock()
    repo.llen = AsyncMock()
    return repo


@pytest.fixture
def mock_connection_manager():
    """Mock连接管理器"""
    manager = MagicMock(spec=ConnectionManager)

    # Mock各种连接器
    mock_redis_connector = AsyncMock()
    mock_postgres_connector = AsyncMock()
    mock_mysql_connector = AsyncMock()
    mock_mongodb_connector = AsyncMock()
    mock_rabbitmq_connector = AsyncMock()

    manager.get_connector.side_effect = lambda name: {
        'redis': mock_redis_connector,
        'postgres': mock_postgres_connector,
        'mysql': mock_mysql_connector,
        'mongodb': mock_mongodb_connector,
        'rabbitmq': mock_rabbitmq_connector
    }.get(name)

    return manager


@pytest.fixture
def mock_jwt_manager():
    """Mock JWT管理器"""
    manager = MagicMock(spec=JWTManager)
    manager.generate_token_pair = MagicMock(return_value={
        "access_token": "mock_access_token",
        "refresh_token": "mock_refresh_token",
        "expires_in": 7200,
        "refresh_expires_in": 86400
    })
    manager.verify_token = MagicMock(return_value={"user_id": "test_user"})
    manager.decode_token = MagicMock(return_value={"user_id": "test_user"})
    manager.revoke_token = AsyncMock()
    return manager


@pytest.fixture
def mock_session_manager():
    """Mock会话管理器"""
    manager = AsyncMock(spec=SessionManager)
    manager.create_session = AsyncMock(return_value=MagicMock(session_id="test_session"))
    manager.get_session = AsyncMock()
    manager.update_session = AsyncMock()
    manager.delete_session = AsyncMock()
    manager.cleanup_expired_sessions = AsyncMock()
    return manager


@pytest.fixture
def mock_cache_manager():
    """Mock缓存管理器"""
    manager = AsyncMock(spec=CacheManager)
    manager.get = AsyncMock()
    manager.set = AsyncMock()
    manager.delete = AsyncMock()
    manager.clear = AsyncMock()
    manager.get_stats = AsyncMock(return_value={"hits": 0, "misses": 0})
    return manager


@pytest.fixture
def mock_security_utils():
    """Mock安全工具"""
    utils = MagicMock(spec=SecurityUtils)
    utils.hash_password = MagicMock(return_value="hashed_password")
    utils.verify_password = MagicMock(return_value=True)
    utils.generate_salt = MagicMock(return_value="test_salt")
    utils.generate_random_string = MagicMock(return_value="random_string")
    utils.verify_totp = MagicMock(return_value=True)
    utils.generate_totp_secret = MagicMock(return_value="totp_secret")
    return utils


@pytest.fixture
def mock_email_service():
    """Mock邮件服务"""
    service = AsyncMock()
    service.send_email = AsyncMock(return_value=True)
    service.send_verification_email = AsyncMock(return_value=True)
    service.send_password_reset_email = AsyncMock(return_value=True)
    return service


@pytest.fixture
def mock_sms_service():
    """Mock短信服务"""
    service = AsyncMock()
    service.send_sms = AsyncMock(return_value=True)
    service.send_verification_code = AsyncMock(return_value=True)
    return service


@pytest.fixture
def mock_verification_service():
    """Mock验证服务"""
    service = AsyncMock()
    service.generate_code = AsyncMock(return_value="123456")
    service.verify_code = AsyncMock(return_value=True)
    service.send_email_verification = AsyncMock(return_value=True)
    service.send_sms_verification = AsyncMock(return_value=True)
    return service


@pytest.fixture
def mock_database_models():
    """Mock数据库模型"""
    return {
        "User": MagicMock(),
        "Tenant": MagicMock(),
        "Role": MagicMock(),
        "Permission": MagicMock(),
        "UserRole": MagicMock(),
        "RolePermission": MagicMock(),
        "AuditLog": MagicMock(),
        "SystemConfig": MagicMock(),
        "SecurityPolicy": MagicMock(),
        "SecurityEvent": MagicMock()
    }


@pytest.fixture
def mock_business_services():
    """Mock业务服务"""
    return {
        "tenant_service": AsyncMock(),
        "user_service": AsyncMock(),
        "auth_service": AsyncMock(),
        "role_service": AsyncMock(),
        "permission_service": AsyncMock(),
        "rbac_service": AsyncMock(),
        "audit_service": AsyncMock(),
        "system_config_service": AsyncMock(),
        "advanced_security_service": AsyncMock(),
        "system_service": AsyncMock()
    }


@pytest.fixture
def mock_config_data():
    """Mock配置数据"""
    return {
        "application": {
            "project_name": "test_iam_service",
            "debug": True,
            "title": "Test IAM Service",
            "description": "Test IAM Service API"
        },
        "persistence": {
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 15
            },
            "postgres": {
                "host": "localhost",
                "port": 5432,
                "database": "test_iam"
            }
        },
        "security": {
            "jwt": {
                "algorithm": "RS256",
                "access_token_expire_minutes": 30,
                "refresh_token_expire_days": 7
            },
            "session": {
                "session_timeout_minutes": 60,
                "max_concurrent_sessions": 5
            }
        }
    }


@pytest.fixture
def mock_provider_overrides():
    """Mock provider覆盖配置"""
    return {
        "session": AsyncMock(),
        "redis_repo": AsyncMock(),
        "jwt_manager": MagicMock(),
        "session_manager": AsyncMock(),
        "security_utils": MagicMock()
    }


# ==================== INTEGRATION TEST FIXTURES ====================

@pytest.fixture
def integration_test_config():
    """集成测试配置"""
    return {
        "application": {
            "project_name": "integration_test_iam_service",
            "debug": True,
            "title": "Integration Test IAM Service"
        },
        "persistence": {
            "redis": {"host": "localhost", "port": 6379, "db": 15},
            "postgres": {"host": "localhost", "port": 5432, "database": "integration_test_iam"}
        },
        "connection_priority": {"redis": 1, "postgres": 2},
        "log_dir": "/tmp/integration_test_logs"
    }


@pytest.fixture
def full_di_flow_config():
    """完整DI流程配置"""
    return {
        "application": {
            "project_name": "full_di_test_iam_service",
            "debug": True,
            "title": "Full DI Test IAM Service"
        },
        "persistence": {
            "redis": {"host": "localhost", "port": 6379, "db": 15},
            "postgres": {"host": "localhost", "port": 5432, "database": "full_di_test_iam"}
        },
        "connection_priority": {"redis": 1, "postgres": 2},
        "security": {
            "jwt": {
                "algorithm": "RS256",
                "access_token_expire_minutes": 30,
                "refresh_token_expire_days": 7,
                "private_key": "test_private_key",
                "public_key": "test_public_key"
            },
            "session": {
                "session_timeout_minutes": 60,
                "max_concurrent_sessions": 5,
                "enable_device_tracking": True
            },
            "cache": {
                "default_ttl": 3600,
                "max_size": 1000
            }
        },
        "log_dir": "/tmp/full_di_test_logs"
    }


@pytest.fixture
def temp_integration_config_file(integration_test_config):
    """临时集成测试配置文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(integration_test_config, f)
        temp_path = f.name

    yield temp_path

    # 清理
    if os.path.exists(temp_path):
        os.unlink(temp_path)


@pytest.fixture
def mock_app_initializer():
    """Mock应用初始化器"""
    mock_config = MagicMock()
    mock_infra = MagicMock()
    mock_services = MagicMock()
    wire_modules = ["routes.tenants", "services.tenant_service"]

    return AppInitializer(
        config=mock_config,
        infra=mock_infra,
        services=mock_services,
        wire_modules=wire_modules
    )


@pytest.fixture
def container_integration_mocks():
    """容器集成测试Mock对象"""
    return {
        "config_container": MagicMock(),
        "infra_container": MagicMock(),
        "service_container": MagicMock(),
        "connection_manager": MagicMock(),
        "redis_client": AsyncMock(),
        "postgres_client": AsyncMock(),
        "mysql_client": AsyncMock()
    }


@pytest.fixture
def di_flow_test_mocks():
    """DI流程测试Mock对象"""
    return {
        "config_loader": MagicMock(),
        "config_object": MagicMock(),
        "redis_repo": AsyncMock(),
        "session": AsyncMock(),
        "jwt_manager": MagicMock(),
        "session_manager": AsyncMock(),
        "security_utils": MagicMock(),
        "tenant_service": MagicMock(),
        "user_service": MagicMock(),
        "auth_service": MagicMock()
    }


@pytest.fixture
def security_integration_mocks():
    """安全组件集成测试Mock对象"""
    return {
        "security_config_manager": MagicMock(),
        "jwt_config": MagicMock(),
        "session_config": MagicMock(),
        "cache_config": MagicMock(),
        "jwt_manager": MagicMock(),
        "session_manager": AsyncMock(),
        "cache_manager": AsyncMock(),
        "security_utils": MagicMock()
    }


@pytest.fixture
def external_services_integration_mocks():
    """外部服务集成测试Mock对象"""
    return {
        "sms_provider": MagicMock(),
        "sms_service": AsyncMock(),
        "email_service": AsyncMock(),
        "verification_service": AsyncMock()
    }


@pytest.fixture
def business_services_integration_mocks():
    """业务服务集成测试Mock对象"""
    return {
        "tenant_service": MagicMock(),
        "user_service": MagicMock(),
        "auth_service": MagicMock(),
        "role_service": MagicMock(),
        "permission_service": MagicMock(),
        "rbac_service": MagicMock(),
        "audit_service": MagicMock()
    }


@pytest.fixture
def container_lifecycle_mocks():
    """容器生命周期测试Mock对象"""
    return {
        "init_resources": MagicMock(),
        "shutdown_resources": MagicMock(),
        "wire": MagicMock(),
        "unwire": MagicMock()
    }


@pytest.fixture
def error_simulation_mocks():
    """错误模拟测试Mock对象"""
    return {
        "config_error": Exception("Config error"),
        "connection_error": ConnectionError("Connection failed"),
        "timeout_error": TimeoutError("Timeout"),
        "integrity_error": Exception("Integrity error"),
        "operational_error": Exception("Operational error")
    }


# ==================== INTEGRATION TEST UTILITIES ====================

@pytest.fixture
def integration_test_helper():
    """集成测试辅助工具"""

    class IntegrationTestHelper:
        @staticmethod
        def create_mock_container_chain():
            """创建Mock容器链"""
            config = MagicMock()
            infra = MagicMock()
            services = MagicMock()

            # 设置容器关系
            infra.config = config
            services.config = config
            services.infra = infra

            return config, infra, services

        @staticmethod
        def setup_mock_dependencies(services_container):
            """设置Mock依赖"""
            mock_session = AsyncMock()
            mock_redis_repo = AsyncMock()
            mock_jwt_manager = MagicMock()

            services_container.session.override(mock_session)
            services_container.redis_repo.override(mock_redis_repo)
            services_container.jwt_manager.override(mock_jwt_manager)

            return mock_session, mock_redis_repo, mock_jwt_manager

        @staticmethod
        def verify_container_initialization(config, infra, services):
            """验证容器初始化"""
            assert config is not None
            assert infra is not None
            assert services is not None
            assert services.config is config
            assert services.infra is infra

    return IntegrationTestHelper()


@pytest.fixture
def di_flow_test_helper():
    """DI流程测试辅助工具"""

    class DIFlowTestHelper:
        @staticmethod
        def create_temp_config_file(config_data):
            """创建临时配置文件"""
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(config_data, f)
                return f.name

        @staticmethod
        def cleanup_temp_file(file_path):
            """清理临时文件"""
            if os.path.exists(file_path):
                os.unlink(file_path)

        @staticmethod
        def setup_config_mocks():
            """设置配置Mock对象"""
            mock_loader = MagicMock()
            mock_config_obj = MagicMock()

            mock_config_obj.application.project_name = "test_iam_service"
            mock_config_obj.debug = True
            mock_config_obj.log_dir = "/tmp/test_logs"
            mock_config_obj.persistence = MagicMock()
            mock_config_obj.connection_priority = MagicMock()

            mock_loader.get_config.return_value = mock_config_obj
            return mock_loader, mock_config_obj

        @staticmethod
        def setup_connection_mocks():
            """设置连接Mock对象"""
            mock_connection_manager = MagicMock()
            mock_redis_client = AsyncMock()
            mock_postgres_client = AsyncMock()

            mock_connection_manager.get_connector.side_effect = lambda name: {
                'redis': mock_redis_client,
                'postgres': mock_postgres_client
            }.get(name)

            return mock_connection_manager, mock_redis_client, mock_postgres_client

    return DIFlowTestHelper()
