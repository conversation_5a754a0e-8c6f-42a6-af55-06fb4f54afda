"""
测试配置文件

提供测试所需的 fixtures 和配置
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from domain_common.models import CommonStatus
from domain_common.models.iam_models import Role, Permission, User, Tenant, AuditLog, RolePermission, UserRole
from main import app
from container import ServiceContainer
from commonlib.storages.persistence.redis.repository import RedisRepository
from services import TenantService


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    session = AsyncMock(spec=AsyncSession)
    session.add = MagicMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.flush = AsyncMock()
    session.delete = AsyncMock()
    return session


@pytest.fixture
def mock_redis_repo():
    """模拟 Redis 仓储"""
    mock_repo = AsyncMock(spec=RedisRepository)

    # 设置默认返回值
    mock_repo.set.return_value = None
    mock_repo.get.return_value = None
    mock_repo.delete.return_value = None
    mock_repo.keys.return_value = []
    mock_repo.exists.return_value = False
    mock_repo.expire.return_value = None
    mock_repo.ttl.return_value = -1
    mock_repo.incr.return_value = 1
    mock_repo.decr.return_value = 0
    mock_repo.lpush.return_value = None
    mock_repo.rpush.return_value = None
    mock_repo.lpop.return_value = None
    mock_repo.rpop.return_value = None
    mock_repo.llen.return_value = 0
    mock_repo.lrange.return_value = []
    mock_repo.ltrim.return_value = None
    mock_repo.sadd.return_value = None
    mock_repo.srem.return_value = None
    mock_repo.smembers.return_value = set()
    mock_repo.sismember.return_value = False
    mock_repo.clear_all.return_value = True
    mock_repo.delete_pattern.return_value = True
    mock_repo.ping.return_value = True

    return mock_repo


@pytest.fixture
def mock_tenant_service():
    """模拟租户服务"""
    mock_service = AsyncMock()
    mock_service.create_tenant.return_value = {
        "tenant_id": "test_tenant",
        "tenant_name": "测试租户",
        "status": "pending"
    }
    mock_service.list_tenants.return_value = {
        "items": [],
        "has_more": False,
        "total": 0
    }
    return mock_service


@pytest.fixture
def mock_user_service():
    """模拟用户服务"""
    mock_service = AsyncMock()
    mock_service.create_user.return_value = {
        "user_id": "test_user",
        "username": "testuser",
        "status": "pending"
    }
    mock_service.list_users.return_value = {
        "items": [],
        "has_more": False,
        "total": 0
    }
    return mock_service


@pytest.fixture
def mock_auth_service():
    """模拟认证服务"""
    mock_service = AsyncMock()
    mock_service.login.return_value = {
        "access_token": "test_token",
        "token_type": "Bearer",
        "expires_in": 7200
    }
    mock_service.logout.return_value = {
        "status": "success"
    }
    return mock_service


@pytest.fixture
def sample_tenant_data():
    """示例租户数据"""
    return {
        "tenant_name": "测试企业",
        "tenant_code": "TEST",
        "description": "这是一个测试企业",
        "settings": {
            "max_users": 100,
            "session_timeout": 3600
        }
    }


@pytest.fixture
def sample_user_data():
    """示例用户数据"""
    return {
        "tenant_id": "test_tenant",
        "username": "testuser",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "nickname": "测试用户",
        "password": "Test123456!"
    }


@pytest.fixture
def sample_login_data():
    """示例登录数据"""
    return {
        "tenant_id": "test_tenant",
        "username": "testuser",
        "password": "Test123456!",
        "login_type": "password"
    }


@pytest.fixture
def sample_request_data():
    """示例请求数据"""
    return {
        "meta": {
            "request_id": "test_req_123",
            "timestamp": "2025-01-22 10:30:45.123",
            "version": "v1"
        },
        "data": {}
    }


# JWT测试相关的fixtures
@pytest.fixture
def sample_jwt_config():
    """示例JWT配置"""
    return {
        "algorithm": "RS256",
        "access_token_expire_minutes": 30,
        "refresh_token_expire_days": 7,
        "issuer": "test-iam-service",
        "audience": "test-rag-platform"
    }


@pytest.fixture
def sample_session_config():
    """示例会话配置"""
    return {
        "session_timeout_minutes": 120,
        "max_concurrent_sessions": 5,
        "enable_device_tracking": True,
        "enable_ip_tracking": True,
        "check_device_fingerprint": True,
        "check_ip_change": True
    }


@pytest.fixture
def sample_cache_config():
    """示例缓存配置"""
    return {
        "user_info_ttl": 3600,
        "user_roles_ttl": 1800,
        "user_permissions_ttl": 1800,
        "role_permissions_ttl": 3600,
        "session_ttl": 7200,
        "permission_check_ttl": 300
    }


@pytest.fixture
def sample_session():
    """示例会话数据"""
    return {
        "session_id": "test_session_789",
        "user_id": "test_user_123",
        "tenant_id": "test_tenant_456",
        "device_fingerprint": "test_device_abc123",
        "ip_address": "*************",
        "user_agent": "Mozilla/5.0 (Test Browser)",
        "is_active": True
    }


@pytest.fixture
def sample_device_info():
    """示例设备信息"""
    return {
        "fingerprint": "test_device_abc123",
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "screen_resolution": "1920x1080",
        "timezone": "Asia/Shanghai",
        "language": "zh-CN",
        "platform": "Win32",
        "plugins": ["Chrome PDF Plugin", "Chrome PDF Viewer"],
        "canvas_fingerprint": "canvas_hash_123456"
    }


def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "performance: marks tests as performance tests"
    )
    config.addinivalue_line(
        "markers", "security: marks tests as security tests"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 为集成测试添加慢速标记
    for item in items:
        if "integration" in item.keywords:
            item.add_marker(pytest.mark.slow)

        if "performance" in item.keywords:
            item.add_marker(pytest.mark.slow)


@pytest.fixture(autouse=True)
async def cleanup_test_data():
    """自动清理测试数据"""
    # 测试前的设置
    yield

    # 测试后的清理
    # 这里可以添加清理逻辑，比如清理测试Redis数据等
    pass


# 性能测试相关的fixtures
@pytest.fixture
def performance_config():
    """性能测试配置"""
    return {
        "token_generation_target_rps": 100,  # 每秒生成令牌数目标
        "token_verification_target_rps": 500,  # 每秒验证令牌数目标
        "max_memory_increase_mb": 100,  # 最大内存增长（MB）
        "max_token_size_bytes": 8192,  # 最大令牌大小（字节）
    }


@pytest.fixture
def security_test_vectors():
    """安全测试向量"""
    return {
        "invalid_tokens": [
            "",
            "invalid.token.here",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid",
            "Bearer invalid_token",
            None
        ],
        "malicious_payloads": [
            {"user_id": "../../../etc/passwd"},
            {"user_id": "<script>alert('xss')</script>"},
            {"user_id": "'; DROP TABLE users; --"},
            {"roles": ["admin"] * 10000},  # 大量角色
            {"permissions": ["perm"] * 100000}  # 大量权限
        ],
        "suspicious_ips": [
            ("***********", "********"),  # 内网到内网
            ("***********", "*******"),  # 内网到公网
            ("*******", "*******"),  # 公网到公网
        ],
        "device_fingerprints": [
            "normal_device_123",
            "suspicious_device_456",
            "mobile_device_789",
            "bot_device_000"
        ]
    }


@pytest.fixture
def tenant_service(mock_session, mock_redis_repo):
    """创建租户服务实例"""
    return TenantService(
        session=mock_session,
        redis_repo=mock_redis_repo,
        user_model=User,
        tenant_model=Tenant,
        role_model=Role,
        permission_model=Permission,
        user_role_model=UserRole,
        role_permission_model=RolePermission,
        audit_log_model=AuditLog
    )


@pytest.fixture
def sample_tenant():
    """示例租户对象"""
    tenant = MagicMock(spec=Tenant)
    tenant.tenant_id = "tenant_123"
    tenant.tenant_name = "测试企业"
    tenant.tenant_code = "TEST_CORP"
    tenant.description = "这是一个测试企业"
    tenant.status = CommonStatus.ACTIVE
    tenant.max_users = 1000
    tenant.settings = {
        "session_timeout": 3600,
        "password_policy": {
            "min_length": 8,
            "require_special_char": True
        }
    }
    tenant.created_at = "2025-01-23T10:30:45"
    tenant.updated_at = "2025-01-23T11:00:00"
    tenant.deleted_at = None
    return tenant


@pytest.fixture
def sample_user():
    """示例用户对象"""
    user = MagicMock(spec=User)
    user.user_id = "user_123"
    user.tenant_id = "tenant_123"
    user.username = "testuser"
    user.email = "<EMAIL>"
    user.phone = "13800138000"
    user.nickname = "测试用户"
    user.status = CommonStatus.ACTIVE
    user.created_at = "2025-01-23T10:30:45"
    user.updated_at = "2025-01-23T11:00:00"
    user.deleted_at = None
    return user


@pytest.fixture
def sample_role():
    """示例角色对象"""
    role = MagicMock(spec=Role)
    role.role_id = "role_123"
    role.tenant_id = "tenant_123"
    role.role_name = "管理员"
    role.role_code = "ADMIN"
    role.description = "管理员角色"
    role.level = 2
    role.status = CommonStatus.ACTIVE
    role.created_at = "2025-01-23T10:30:45"
    role.updated_at = "2025-01-23T11:00:00"
    role.deleted_at = None
    return role


@pytest.fixture
def sample_permission():
    """示例权限对象"""
    permission = MagicMock(spec=Permission)
    permission.permission_id = "perm_123"
    permission.tenant_id = "tenant_123"
    permission.permission_name = "用户管理"
    permission.permission_code = "user:manage"
    permission.resource = "user"
    permission.action = "manage"
    permission.level = 2
    permission.status = CommonStatus.ACTIVE
    permission.created_at = "2025-01-23T10:30:45"
    permission.updated_at = "2025-01-23T11:00:00"
    permission.deleted_at = None
    return permission


@pytest.fixture
def sample_tenant_data():
    """示例租户创建数据"""
    return {
        "tenant_name": "测试企业",
        "tenant_code": "TEST_CORP",
        "description": "这是一个测试企业",
        "max_users": 500,
        "settings": {
            "session_timeout": 3600,
            "password_policy": {
                "min_length": 8,
                "require_special_char": True
            }
        }
    }


@pytest.fixture
def sample_admin_user_data():
    """示例管理员用户数据"""
    return {
        "username": "admin",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "password": "Admin123!@#",
        "nickname": "管理员"
    }


@pytest.fixture
def sample_api_request():
    """示例API请求数据"""
    return {
        "meta": {
            "request_id": "req_123",
            "timestamp": "2025-01-23 10:30:45.123",
            "version": "v1"
        },
        "data": {}
    }


@pytest.fixture
def mock_database_success(mock_session):
    """模拟数据库操作成功"""
    mock_session.execute.return_value.scalar_one_or_none.return_value = None
    mock_session.execute.return_value.scalar.return_value = 0
    mock_session.execute.return_value.scalars.return_value.all.return_value = []
    return mock_session


@pytest.fixture
def mock_database_with_tenant(mock_session, sample_tenant):
    """模拟数据库包含租户数据"""
    mock_session.execute.return_value.scalar_one_or_none.return_value = sample_tenant
    mock_session.execute.return_value.scalar.return_value = 5  # 用户数量
    mock_session.execute.return_value.scalars.return_value.all.return_value = [sample_tenant]
    return mock_session


@pytest.fixture
def mock_redis_with_cache(mock_redis_repo):
    """模拟Redis包含缓存数据"""
    cached_data = {
        "tenant_id": "tenant_123",
        "tenant_name": "测试企业",
        "tenant_code": "TEST_CORP",
        "status": CommonStatus.ACTIVE
    }
    mock_redis_repo.get.return_value = cached_data
    return mock_redis_repo


# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "performance: 性能测试"
    )
    config.addinivalue_line(
        "markers", "edge_case: 边界测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )


# 测试数据清理
@pytest.fixture(autouse=True)
def cleanup_test_data():
    """自动清理测试数据"""
    yield
    # 在这里可以添加测试后的清理逻辑
    # 例如清理测试数据库、缓存等
    pass


# 异步测试支持
@pytest.fixture
def async_test():
    """异步测试支持"""

    def _async_test(coro):
        return asyncio.get_event_loop().run_until_complete(coro)

    return _async_test


# 错误模拟
@pytest.fixture
def mock_database_error(mock_session):
    """模拟数据库错误"""
    from sqlalchemy.exc import IntegrityError, OperationalError

    def set_error(error_type="integrity"):
        if error_type == "integrity":
            mock_session.commit.side_effect = IntegrityError("statement", "params", "orig")
        elif error_type == "operational":
            mock_session.execute.side_effect = OperationalError("statement", "params", "orig")
        elif error_type == "timeout":
            mock_session.execute.side_effect = OperationalError("statement", "params", "timeout")

    mock_session.set_error = set_error
    return mock_session


@pytest.fixture
def mock_redis_error(mock_redis_repo):
    """模拟Redis错误"""

    def set_error(error_type="connection"):
        if error_type == "connection":
            mock_redis_repo.get.side_effect = ConnectionError("Redis connection failed")
            mock_redis_repo.set.side_effect = ConnectionError("Redis connection failed")
            mock_redis_repo.delete.side_effect = ConnectionError("Redis connection failed")
        elif error_type == "timeout":
            mock_redis_repo.get.side_effect = TimeoutError("Redis timeout")
            mock_redis_repo.set.side_effect = TimeoutError("Redis timeout")

    mock_redis_repo.set_error = set_error
    return mock_redis_repo


# 性能测试配置
@pytest.fixture
def performance_config():
    """性能测试配置"""
    return {
        "max_execution_time": 1.0,  # 最大执行时间（秒）
        "max_memory_usage": 100,  # 最大内存使用（MB）
        "batch_size": 10,  # 批量操作大小
        "concurrent_operations": 30  # 并发操作数量
    }


# 测试环境配置
@pytest.fixture
def test_config():
    """测试环境配置"""
    return {
        "database_url": "sqlite:///:memory:",
        "redis_url": "redis://localhost:6379/15",  # 使用测试数据库
        "log_level": "DEBUG",
        "test_mode": True
    }
