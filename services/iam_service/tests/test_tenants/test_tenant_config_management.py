"""
租户配置管理功能测试模块

测试租户配置管理相关功能，包括：
- 租户设置管理
- 租户状态管理
- 自动状态管理
- 配额管理
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime
from typing import Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession
from domain_common.models.iam_models import Tenant, User, Role, Permission, UserRole, RolePermission, AuditLog
from domain_common.models.constants import CommonStatus
from domain_common.exceptions import NotFoundError, ValidationError, BusinessError, DatabaseError
from infrastructure.repositories.redis_repository import RedisRepository
from services.tenant_service import TenantService


class TestTenantConfigManagement:
    """租户配置管理测试类"""

    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def mock_redis_repo(self):
        """模拟Redis仓储"""
        redis_repo = AsyncMock(spec=RedisRepository)
        return redis_repo

    @pytest.fixture
    def tenant_service(self, mock_session, mock_redis_repo):
        """创建租户服务实例"""
        return TenantService(
            session=mock_session,
            redis_repo=mock_redis_repo,
            user_model=User,
            tenant_model=Tenant,
            role_model=Role,
            permission_model=Permission,
            user_role_model=UserRole,
            role_permission_model=RolePermission,
            audit_log_model=AuditLog
        )

    @pytest.fixture
    def sample_tenant(self):
        """示例租户数据"""
        tenant = MagicMock(spec=Tenant)
        tenant.tenant_id = "test-tenant-id"
        tenant.tenant_name = "测试租户"
        tenant.tenant_code = "TEST_TENANT"
        tenant.status = CommonStatus.ACTIVE
        tenant.settings = {
            "password_policy": {
                "min_length": 8,
                "require_uppercase": True
            }
        }
        tenant.updated_at = datetime.now()
        return tenant

    # ================================
    # 租户设置管理测试
    # ================================

    @pytest.mark.asyncio
    async def test_get_tenant_settings_success(self, tenant_service, mock_session, sample_tenant):
        """测试成功获取租户配置"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_tenant
        mock_session.execute.return_value = mock_result

        # 执行测试
        result = await tenant_service.get_tenant_settings("test-tenant-id")

        # 验证结果
        assert result["tenant_id"] == "test-tenant-id"
        assert "settings" in result
        assert "password_policy" in result["settings"]
        assert result["settings"]["password_policy"]["min_length"] == 8
        assert "last_updated" in result
        assert "settings_version" in result

    @pytest.mark.asyncio
    async def test_get_tenant_settings_not_found(self, tenant_service, mock_session):
        """测试获取不存在租户的配置"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # 执行测试并验证异常
        with pytest.raises(NotFoundError) as exc_info:
            await tenant_service.get_tenant_settings("non-existent-tenant")
        
        assert "租户" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_tenant_settings_success(self, tenant_service, mock_session, mock_redis_repo, sample_tenant):
        """测试成功更新租户配置"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_tenant
        mock_session.execute.return_value = mock_result

        settings_update = {
            "password_policy": {
                "min_length": 10,
                "require_special_chars": True
            }
        }

        # 执行测试
        result = await tenant_service.update_tenant_settings(
            tenant_id="test-tenant-id",
            settings_update=settings_update,
            merge_mode="merge"
        )

        # 验证结果
        assert result["tenant_id"] == "test-tenant-id"
        assert result["merge_mode"] == "merge"
        assert "password_policy" in result["updated_keys"]
        assert "updated_at" in result

        # 验证数据库操作
        mock_session.commit.assert_called_once()
        
        # 验证缓存清理
        mock_redis_repo.delete.assert_called()

    @pytest.mark.asyncio
    async def test_validate_password_policy_success(self, tenant_service):
        """测试密码策略验证成功"""
        policy = {
            "min_length": 10,
            "max_length": 64,
            "require_uppercase": True,
            "require_lowercase": True,
            "require_digits": True,
            "require_special_chars": True,
            "password_history_count": 3,
            "password_expiry_days": 90
        }

        result = await tenant_service._validate_password_policy(policy)

        assert result["min_length"] == 10
        assert result["max_length"] == 64
        assert result["require_uppercase"] is True
        assert result["password_history_count"] == 3

    @pytest.mark.asyncio
    async def test_validate_password_policy_invalid_length(self, tenant_service):
        """测试密码策略验证失败 - 无效长度"""
        policy = {
            "min_length": 2,  # 太短
            "max_length": 64
        }

        with pytest.raises(ValidationError) as exc_info:
            await tenant_service._validate_password_policy(policy)
        
        assert "密码最小长度" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_session_config_success(self, tenant_service):
        """测试会话配置验证成功"""
        config = {
            "session_timeout": 3600,
            "max_concurrent_sessions": 3,
            "idle_timeout": 1800,
            "remember_me_duration": 2592000
        }

        result = await tenant_service._validate_session_config(config)

        assert result["session_timeout"] == 3600
        assert result["max_concurrent_sessions"] == 3
        assert result["idle_timeout"] == 1800

    @pytest.mark.asyncio
    async def test_validate_session_config_invalid_timeout(self, tenant_service):
        """测试会话配置验证失败 - 无效超时时间"""
        config = {
            "session_timeout": 100,  # 太短
            "idle_timeout": 60
        }

        with pytest.raises(ValidationError) as exc_info:
            await tenant_service._validate_session_config(config)
        
        assert "会话超时时间" in str(exc_info.value)

    # ================================
    # 租户状态管理测试
    # ================================

    @pytest.mark.asyncio
    async def test_change_tenant_status_success(self, tenant_service, mock_session, mock_redis_repo, sample_tenant):
        """测试成功变更租户状态"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_tenant
        mock_session.execute.return_value = mock_result

        # 执行测试
        result = await tenant_service.change_tenant_status(
            tenant_id="test-tenant-id",
            new_status=CommonStatus.SUSPENDED,
            reason="admin_action",
            comment="测试暂停"
        )

        # 验证结果
        assert result["tenant_id"] == "test-tenant-id"
        assert result["old_status"] == CommonStatus.ACTIVE
        assert result["new_status"] == CommonStatus.SUSPENDED
        assert result["reason"] == "admin_action"
        assert result["comment"] == "测试暂停"

        # 验证数据库操作
        mock_session.commit.assert_called_once()
        
        # 验证缓存清理
        mock_redis_repo.delete.assert_called()

    @pytest.mark.asyncio
    async def test_validate_status_transition_success(self, tenant_service):
        """测试状态转换验证成功"""
        # 测试合法的状态转换
        result = await tenant_service.validate_status_transition(
            current_status=CommonStatus.PENDING,
            target_status=CommonStatus.ACTIVE,
            reason="admin_action"
        )
        
        assert result is True

    @pytest.mark.asyncio
    async def test_validate_status_transition_invalid(self, tenant_service):
        """测试状态转换验证失败"""
        # 测试非法的状态转换
        with pytest.raises(ValidationError) as exc_info:
            await tenant_service.validate_status_transition(
                current_status=CommonStatus.DELETED,
                target_status=CommonStatus.ACTIVE,
                reason="admin_action"
            )
        
        assert "不允许从状态" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_status_transition_same_status(self, tenant_service):
        """测试相同状态转换验证失败"""
        with pytest.raises(ValidationError) as exc_info:
            await tenant_service.validate_status_transition(
                current_status=CommonStatus.ACTIVE,
                target_status=CommonStatus.ACTIVE,
                reason="admin_action"
            )
        
        assert "目标状态与当前状态相同" in str(exc_info.value)

    def test_get_status_transition_rules(self, tenant_service):
        """测试获取状态转换规则"""
        rules = tenant_service.get_status_transition_rules()
        
        assert "transition_rules" in rules
        assert "change_reasons" in rules
        assert "status_descriptions" in rules
        
        # 验证规则内容
        assert CommonStatus.PENDING in rules["transition_rules"]
        assert CommonStatus.ACTIVE in rules["transition_rules"][CommonStatus.PENDING]
        assert "admin_action" in rules["change_reasons"]

    # ================================
    # 配额管理测试
    # ================================

    @pytest.mark.asyncio
    async def test_check_tenant_quotas_success(self, tenant_service, mock_session, sample_tenant):
        """测试成功检查租户配额"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_tenant
        mock_session.execute.return_value = mock_result

        # 模拟用户数量查询
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 25
        mock_session.execute.return_value = mock_count_result

        # 执行测试
        result = await tenant_service.check_tenant_quotas("test-tenant-id")

        # 验证结果
        assert result["tenant_id"] == "test-tenant-id"
        assert "has_violations" in result
        assert "quotas" in result
        assert "users" in result["quotas"]

    @pytest.mark.asyncio
    async def test_check_user_quota_normal(self, tenant_service, mock_session):
        """测试用户配额检查 - 正常情况"""
        # 模拟用户数量查询
        mock_result = AsyncMock()
        mock_result.scalar.return_value = 25
        mock_session.execute.return_value = mock_result

        result = await tenant_service._check_user_quota("test-tenant-id", 1000)

        assert result["quota_type"] == "users"
        assert result["limit"] == 1000
        assert result["current"] == 25
        assert result["available"] == 975
        assert result["exceeded"] is False
        assert result["warning"] is False

    @pytest.mark.asyncio
    async def test_check_user_quota_exceeded(self, tenant_service, mock_session):
        """测试用户配额检查 - 超限情况"""
        # 模拟用户数量查询
        mock_result = AsyncMock()
        mock_result.scalar.return_value = 1500
        mock_session.execute.return_value = mock_result

        result = await tenant_service._check_user_quota("test-tenant-id", 1000)

        assert result["quota_type"] == "users"
        assert result["limit"] == 1000
        assert result["current"] == 1500
        assert result["exceeded"] is True
        assert result["usage_percentage"] == 150.0

    @pytest.mark.asyncio
    async def test_auto_manage_tenant_status_success(self, tenant_service, mock_session, mock_redis_repo):
        """测试自动管理租户状态成功"""
        # 模拟获取租户列表
        mock_result = AsyncMock()
        mock_result.fetchall.return_value = [("tenant1",), ("tenant2",)]
        mock_session.execute.return_value = mock_result

        # 模拟租户数据
        mock_tenant = MagicMock(spec=Tenant)
        mock_tenant.tenant_id = "tenant1"
        mock_tenant.status = CommonStatus.ACTIVE
        mock_tenant.settings = {}
        mock_tenant.max_users = 1000

        mock_tenant_result = AsyncMock()
        mock_tenant_result.scalar_one_or_none.return_value = mock_tenant
        mock_session.execute.return_value = mock_tenant_result

        # 模拟配额检查
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 25
        mock_session.execute.return_value = mock_count_result

        # 执行测试
        result = await tenant_service.auto_manage_tenant_status()

        # 验证结果
        assert "processed_tenants" in result
        assert "status_changes" in result
        assert "quota_violations" in result
        assert "errors" in result

    @pytest.mark.asyncio
    async def test_handle_quota_exceeded_suspend(self, tenant_service, mock_session, sample_tenant):
        """测试处理配额超限 - 暂停租户"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_tenant
        mock_session.execute.return_value = mock_result

        # 执行测试
        result = await tenant_service.handle_quota_exceeded(
            tenant_id="test-tenant-id",
            quota_type="users",
            action="suspend"
        )

        # 验证结果
        assert result["tenant_id"] == "test-tenant-id"
        assert result["old_status"] == CommonStatus.ACTIVE
        assert result["new_status"] == CommonStatus.SUSPENDED
        assert result["reason"] == "quota_exceeded"

    # ================================
    # 配置验证测试
    # ================================

    @pytest.mark.asyncio
    async def test_validate_security_config_success(self, tenant_service):
        """测试安全配置验证成功"""
        config = {
            "max_login_attempts": 5,
            "account_lockout_duration": 1800,
            "require_2fa": False,
            "ip_whitelist_enabled": True,
            "ip_whitelist": ["***********/24", "********"]
        }

        result = await tenant_service._validate_security_config(config)

        assert result["max_login_attempts"] == 5
        assert result["account_lockout_duration"] == 1800
        assert result["require_2fa"] is False
        assert result["ip_whitelist_enabled"] is True
        assert len(result["ip_whitelist"]) == 2

    @pytest.mark.asyncio
    async def test_validate_security_config_invalid_ip(self, tenant_service):
        """测试安全配置验证失败 - 无效IP"""
        config = {
            "ip_whitelist": ["invalid-ip", "***********"]
        }

        with pytest.raises(ValidationError) as exc_info:
            await tenant_service._validate_security_config(config)

        assert "无效的IP地址" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_feature_modules_success(self, tenant_service):
        """测试功能模块配置验证成功"""
        modules = {
            "user_management": True,
            "role_management": True,
            "permission_management": False,
            "audit_logs": True
        }

        result = await tenant_service._validate_feature_modules(modules)

        assert result["user_management"] is True
        assert result["role_management"] is True
        assert result["permission_management"] is False
        assert result["audit_logs"] is True

    @pytest.mark.asyncio
    async def test_validate_feature_modules_all_core_disabled(self, tenant_service):
        """测试功能模块配置验证失败 - 核心模块全部禁用"""
        modules = {
            "user_management": False,
            "role_management": False,
            "permission_management": False
        }

        with pytest.raises(ValidationError) as exc_info:
            await tenant_service._validate_feature_modules(modules)

        assert "至少需要启用一个核心管理模块" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_storage_config_success(self, tenant_service):
        """测试存储配置验证成功"""
        config = {
            "max_storage_mb": 2048,
            "max_file_size_mb": 50,
            "allowed_file_types": ["pdf", "jpg", "png", "doc"]
        }

        result = await tenant_service._validate_storage_config(config)

        assert result["max_storage_mb"] == 2048
        assert result["max_file_size_mb"] == 50
        assert len(result["allowed_file_types"]) == 4
        assert "pdf" in result["allowed_file_types"]

    @pytest.mark.asyncio
    async def test_validate_storage_config_invalid_file_type(self, tenant_service):
        """测试存储配置验证失败 - 不支持的文件类型"""
        config = {
            "allowed_file_types": ["pdf", "exe", "bat"]  # exe和bat不支持
        }

        with pytest.raises(ValidationError) as exc_info:
            await tenant_service._validate_storage_config(config)

        assert "不支持的文件类型" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_notification_config_success(self, tenant_service):
        """测试通知配置验证成功"""
        config = {
            "email_notifications": True,
            "sms_notifications": False,
            "system_notifications": True,
            "notification_limits": {
                "max_per_hour": 50,
                "max_per_day": 500
            }
        }

        result = await tenant_service._validate_notification_config(config)

        assert result["email_notifications"] is True
        assert result["sms_notifications"] is False
        assert result["notification_limits"]["max_per_hour"] == 50
        assert result["notification_limits"]["max_per_day"] == 500

    # ================================
    # 辅助方法测试
    # ================================

    def test_merge_settings_with_defaults(self, tenant_service):
        """测试配置与默认值合并"""
        current_settings = {
            "password_policy": {
                "min_length": 10
            },
            "custom_setting": "custom_value"
        }

        result = tenant_service._merge_settings_with_defaults(current_settings)

        # 验证合并结果
        assert "password_policy" in result
        assert result["password_policy"]["min_length"] == 10  # 保留自定义值
        assert "require_uppercase" in result["password_policy"]  # 包含默认值
        assert "session_config" in result  # 包含默认配置
        assert result["custom_setting"] == "custom_value"  # 保留自定义配置

    def test_deep_merge_settings(self, tenant_service):
        """测试深度合并配置"""
        old_settings = {
            "password_policy": {
                "min_length": 8,
                "require_uppercase": True
            },
            "session_config": {
                "session_timeout": 7200
            }
        }

        new_settings = {
            "password_policy": {
                "min_length": 10,
                "require_special_chars": True
            }
        }

        result = tenant_service._deep_merge_settings(old_settings, new_settings)

        # 验证深度合并结果
        assert result["password_policy"]["min_length"] == 10  # 更新的值
        assert result["password_policy"]["require_uppercase"] is True  # 保留的值
        assert result["password_policy"]["require_special_chars"] is True  # 新增的值
        assert result["session_config"]["session_timeout"] == 7200  # 未变更的配置

    # ================================
    # 错误处理测试
    # ================================

    @pytest.mark.asyncio
    async def test_update_tenant_settings_not_found(self, tenant_service, mock_session):
        """测试更新不存在租户的配置"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # 执行测试并验证异常
        with pytest.raises(NotFoundError):
            await tenant_service.update_tenant_settings(
                tenant_id="non-existent-tenant",
                settings_update={"password_policy": {"min_length": 10}}
            )

    @pytest.mark.asyncio
    async def test_change_tenant_status_not_found(self, tenant_service, mock_session):
        """测试变更不存在租户的状态"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # 执行测试并验证异常
        with pytest.raises(NotFoundError):
            await tenant_service.change_tenant_status(
                tenant_id="non-existent-tenant",
                new_status=CommonStatus.ACTIVE
            )

    @pytest.mark.asyncio
    async def test_check_tenant_quotas_not_found(self, tenant_service, mock_session):
        """测试检查不存在租户的配额"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # 执行测试并验证异常
        with pytest.raises(NotFoundError):
            await tenant_service.check_tenant_quotas("non-existent-tenant")
