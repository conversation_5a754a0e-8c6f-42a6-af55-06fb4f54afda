"""
租户状态管理功能测试模块

专门测试租户状态管理相关功能，包括：
- 状态转换验证
- 状态变更前置条件检查
- 状态变更副作用处理
- 自动状态管理
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from typing import Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession
from domain_common.models.iam_models import Tenant, User, Role, Permission, UserRole, RolePermission, AuditLog
from domain_common.models.constants import CommonStatus
from domain_common.exceptions import NotFoundError, ValidationError, BusinessError, DatabaseError
from infrastructure.repositories.redis_repository import RedisRepository
from services.tenant_service import TenantService


class TestTenantStatusManagement:
    """租户状态管理测试类"""

    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def mock_redis_repo(self):
        """模拟Redis仓储"""
        redis_repo = AsyncMock(spec=RedisRepository)
        return redis_repo

    @pytest.fixture
    def tenant_service(self, mock_session, mock_redis_repo):
        """创建租户服务实例"""
        return TenantService(
            session=mock_session,
            redis_repo=mock_redis_repo,
            user_model=User,
            tenant_model=Tenant,
            role_model=Role,
            permission_model=Permission,
            user_role_model=UserRole,
            role_permission_model=RolePermission,
            audit_log_model=AuditLog
        )

    @pytest.fixture
    def sample_tenant(self):
        """示例租户数据"""
        tenant = MagicMock(spec=Tenant)
        tenant.tenant_id = "test-tenant-id"
        tenant.tenant_name = "测试租户"
        tenant.tenant_code = "TEST_TENANT"
        tenant.status = CommonStatus.ACTIVE
        tenant.settings = {}
        tenant.max_users = 1000
        tenant.updated_at = datetime.now()
        return tenant

    # ================================
    # 状态转换规则测试
    # ================================

    @pytest.mark.parametrize("current_status,target_status,should_succeed", [
        (CommonStatus.PENDING, CommonStatus.ACTIVE, True),
        (CommonStatus.PENDING, CommonStatus.SUSPENDED, True),
        (CommonStatus.PENDING, CommonStatus.DELETED, True),
        (CommonStatus.ACTIVE, CommonStatus.SUSPENDED, True),
        (CommonStatus.ACTIVE, CommonStatus.INACTIVE, True),
        (CommonStatus.ACTIVE, CommonStatus.DELETED, True),
        (CommonStatus.INACTIVE, CommonStatus.ACTIVE, True),
        (CommonStatus.INACTIVE, CommonStatus.SUSPENDED, True),
        (CommonStatus.SUSPENDED, CommonStatus.ACTIVE, True),
        (CommonStatus.SUSPENDED, CommonStatus.INACTIVE, True),
        (CommonStatus.DELETED, CommonStatus.ACTIVE, False),  # 删除状态不能转换
        (CommonStatus.DELETED, CommonStatus.SUSPENDED, False),
        (CommonStatus.ACTIVE, CommonStatus.PENDING, False),  # 不允许的转换
    ])
    @pytest.mark.asyncio
    async def test_status_transition_rules(self, tenant_service, current_status, target_status, should_succeed):
        """测试状态转换规则"""
        if should_succeed:
            result = await tenant_service.validate_status_transition(
                current_status=current_status,
                target_status=target_status,
                reason="admin_action"
            )
            assert result is True
        else:
            with pytest.raises(ValidationError):
                await tenant_service.validate_status_transition(
                    current_status=current_status,
                    target_status=target_status,
                    reason="admin_action"
                )

    @pytest.mark.asyncio
    async def test_validate_status_transition_invalid_status(self, tenant_service):
        """测试无效状态值的验证"""
        with pytest.raises(ValidationError) as exc_info:
            await tenant_service.validate_status_transition(
                current_status="invalid_status",
                target_status=CommonStatus.ACTIVE,
                reason="admin_action"
            )
        assert "无效的当前状态" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_status_transition_invalid_reason(self, tenant_service):
        """测试无效变更原因的验证"""
        with pytest.raises(ValidationError) as exc_info:
            await tenant_service.validate_status_transition(
                current_status=CommonStatus.PENDING,
                target_status=CommonStatus.ACTIVE,
                reason="invalid_reason"
            )
        assert "无效的状态变更原因" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_status_transition_delete_special_reason(self, tenant_service):
        """测试删除状态的特殊原因验证"""
        # 测试允许的删除原因
        result = await tenant_service.validate_status_transition(
            current_status=CommonStatus.ACTIVE,
            target_status=CommonStatus.DELETED,
            reason="admin_action"
        )
        assert result is True

        # 测试不允许的删除原因
        with pytest.raises(ValidationError) as exc_info:
            await tenant_service.validate_status_transition(
                current_status=CommonStatus.ACTIVE,
                target_status=CommonStatus.DELETED,
                reason="user_request"
            )
        assert "删除租户需要管理员操作" in str(exc_info.value)

    # ================================
    # 状态变更前置条件测试
    # ================================

    @pytest.mark.asyncio
    async def test_check_activation_prerequisites_success(self, tenant_service, mock_session):
        """测试激活前置条件检查成功"""
        # 模拟有管理员用户
        mock_result = AsyncMock()
        mock_result.scalar.return_value = 1
        mock_session.execute.return_value = mock_result

        # 应该不抛出异常
        await tenant_service._check_activation_prerequisites("test-tenant-id")

    @pytest.mark.asyncio
    async def test_check_activation_prerequisites_no_admin(self, tenant_service, mock_session):
        """测试激活前置条件检查失败 - 无管理员用户"""
        # 模拟没有管理员用户
        mock_result = AsyncMock()
        mock_result.scalar.return_value = 0
        mock_session.execute.return_value = mock_result

        with pytest.raises(BusinessError) as exc_info:
            await tenant_service._check_activation_prerequisites("test-tenant-id")
        assert "没有可用的管理员用户" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_check_suspension_prerequisites_quota_exceeded(self, tenant_service):
        """测试暂停前置条件检查 - 配额超限"""
        with patch.object(tenant_service, '_check_tenant_quota_status') as mock_check:
            mock_check.return_value = {"exceeded": True}
            
            # 应该不抛出异常
            await tenant_service._check_suspension_prerequisites("test-tenant-id", "quota_exceeded")

    @pytest.mark.asyncio
    async def test_check_suspension_prerequisites_quota_not_exceeded(self, tenant_service):
        """测试暂停前置条件检查失败 - 配额未超限"""
        with patch.object(tenant_service, '_check_tenant_quota_status') as mock_check:
            mock_check.return_value = {"exceeded": False}
            
            with pytest.raises(BusinessError) as exc_info:
                await tenant_service._check_suspension_prerequisites("test-tenant-id", "quota_exceeded")
            assert "配额未超限" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_check_deletion_prerequisites_with_dependencies(self, tenant_service):
        """测试删除前置条件检查失败 - 存在依赖"""
        with patch.object(tenant_service, '_check_tenant_dependencies') as mock_check:
            mock_check.return_value = {
                "has_dependencies": True,
                "dependencies": ["活跃用户: 5个"]
            }
            
            with pytest.raises(BusinessError) as exc_info:
                await tenant_service._check_deletion_prerequisites("test-tenant-id", "admin_action")
            assert "存在依赖关系" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_check_deletion_prerequisites_compliance_issue(self, tenant_service):
        """测试删除前置条件检查 - 合规问题可以强制删除"""
        with patch.object(tenant_service, '_check_tenant_dependencies') as mock_check:
            mock_check.return_value = {
                "has_dependencies": True,
                "dependencies": ["活跃用户: 5个"]
            }
            
            # 合规问题应该不检查依赖关系
            await tenant_service._check_deletion_prerequisites("test-tenant-id", "compliance_issue")

    # ================================
    # 状态变更副作用处理测试
    # ================================

    @pytest.mark.asyncio
    async def test_handle_tenant_suspension(self, tenant_service, mock_redis_repo):
        """测试处理租户暂停"""
        await tenant_service._handle_tenant_suspension("test-tenant-id", "quota_exceeded")
        
        # 验证缓存暂停信息
        mock_redis_repo.set.assert_called_once()
        call_args = mock_redis_repo.set.call_args
        assert call_args[0][0] == "tenant_suspension:test-tenant-id"
        
        # 验证暂停信息内容
        suspension_info = json.loads(call_args[0][1])
        assert suspension_info["suspension_reason"] == "quota_exceeded"
        assert suspension_info["auto_resume_enabled"] is True

    @pytest.mark.asyncio
    async def test_handle_tenant_reactivation(self, tenant_service, mock_redis_repo):
        """测试处理租户重新激活"""
        await tenant_service._handle_tenant_reactivation("test-tenant-id")
        
        # 验证清理暂停信息
        mock_redis_repo.delete.assert_called_with("tenant_suspension:test-tenant-id")

    @pytest.mark.asyncio
    async def test_handle_tenant_deletion(self, tenant_service, mock_redis_repo):
        """测试处理租户删除"""
        await tenant_service._handle_tenant_deletion("test-tenant-id", "admin_action")
        
        # 验证缓存删除信息
        mock_redis_repo.set.assert_called_once()
        call_args = mock_redis_repo.set.call_args
        assert call_args[0][0] == "tenant_deletion:test-tenant-id"
        
        # 验证删除信息内容
        deletion_info = json.loads(call_args[0][1])
        assert deletion_info["deletion_reason"] == "admin_action"
        assert deletion_info["can_recover"] is True

    @pytest.mark.asyncio
    async def test_handle_tenant_deletion_compliance(self, tenant_service, mock_redis_repo):
        """测试处理租户删除 - 合规问题"""
        await tenant_service._handle_tenant_deletion("test-tenant-id", "compliance_issue")
        
        # 验证删除信息内容
        call_args = mock_redis_repo.set.call_args
        deletion_info = json.loads(call_args[0][1])
        assert deletion_info["deletion_reason"] == "compliance_issue"
        assert deletion_info["can_recover"] is False

    @pytest.mark.asyncio
    async def test_send_status_change_notification(self, tenant_service, mock_redis_repo):
        """测试发送状态变更通知"""
        await tenant_service._send_status_change_notification(
            tenant_id="test-tenant-id",
            old_status=CommonStatus.ACTIVE,
            new_status=CommonStatus.SUSPENDED,
            reason="quota_exceeded"
        )
        
        # 验证通知加入队列
        mock_redis_repo.lpush.assert_called_once()
        call_args = mock_redis_repo.lpush.call_args
        assert call_args[0][0] == "tenant_status_notifications"
        
        # 验证通知内容
        notification_data = json.loads(call_args[0][1])
        assert notification_data["tenant_id"] == "test-tenant-id"
        assert notification_data["status_change"]["from"] == CommonStatus.ACTIVE
        assert notification_data["status_change"]["to"] == CommonStatus.SUSPENDED

    # ================================
    # 完整状态变更流程测试
    # ================================

    @pytest.mark.asyncio
    async def test_change_tenant_status_complete_flow(self, tenant_service, mock_session, mock_redis_repo, sample_tenant):
        """测试完整的状态变更流程"""
        # 准备测试数据
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_tenant
        mock_session.execute.return_value = mock_result

        # 模拟前置条件检查通过
        with patch.object(tenant_service, '_check_status_change_prerequisites') as mock_check:
            mock_check.return_value = None
            
            with patch.object(tenant_service, '_handle_status_change_effects') as mock_handle:
                mock_handle.return_value = None
                
                with patch.object(tenant_service, '_send_status_change_notification') as mock_notify:
                    mock_notify.return_value = None
                    
                    # 执行测试
                    result = await tenant_service.change_tenant_status(
                        tenant_id="test-tenant-id",
                        new_status=CommonStatus.SUSPENDED,
                        reason="admin_action",
                        comment="测试暂停",
                        user_id="admin-user-id"
                    )

        # 验证结果
        assert result["tenant_id"] == "test-tenant-id"
        assert result["old_status"] == CommonStatus.ACTIVE
        assert result["new_status"] == CommonStatus.SUSPENDED
        assert result["reason"] == "admin_action"
        assert result["comment"] == "测试暂停"
        assert result["changed_by"] == "admin-user-id"

        # 验证各个步骤都被调用
        mock_check.assert_called_once()
        mock_handle.assert_called_once()
        mock_notify.assert_called_once()
        
        # 验证数据库操作
        mock_session.commit.assert_called_once()
        
        # 验证缓存清理
        assert mock_redis_repo.delete.call_count >= 2  # 至少清理两个缓存

    def test_get_status_transition_rules_structure(self, tenant_service):
        """测试状态转换规则结构"""
        rules = tenant_service.get_status_transition_rules()
        
        # 验证基本结构
        assert isinstance(rules, dict)
        assert "transition_rules" in rules
        assert "change_reasons" in rules
        assert "status_descriptions" in rules
        
        # 验证转换规则
        transition_rules = rules["transition_rules"]
        assert CommonStatus.PENDING in transition_rules
        assert CommonStatus.ACTIVE in transition_rules[CommonStatus.PENDING]
        assert len(transition_rules[CommonStatus.DELETED]) == 0  # 删除状态不能转换
        
        # 验证变更原因
        change_reasons = rules["change_reasons"]
        assert "admin_action" in change_reasons
        assert "quota_exceeded" in change_reasons
        
        # 验证状态描述
        status_descriptions = rules["status_descriptions"]
        assert CommonStatus.ACTIVE in status_descriptions
        assert "正常" in status_descriptions[CommonStatus.ACTIVE]
