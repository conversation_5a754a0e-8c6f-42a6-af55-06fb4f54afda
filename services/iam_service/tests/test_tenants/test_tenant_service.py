"""
租户服务测试用例

测试租户生命周期管理的核心功能
"""

import pytest
import uuid
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from services.tenant_service import TenantService
from domain_common.models.constants import CommonStatus
from domain_common.models.iam_models import Tenant, User, Role, Permission, UserRole, RolePermission, AuditLog
from commonlib.exceptions.exceptions import ValidationError, DuplicateResourceError, NotFoundError, BusinessError
from commonlib.storages.persistence.redis.repository import RedisRepository


class TestTenantService:
    """租户服务测试类"""

    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def mock_redis_repo(self):
        """模拟Redis仓储"""
        redis_repo = AsyncMock(spec=RedisRepository)
        return redis_repo

    @pytest.fixture
    def tenant_service(self, mock_session, mock_redis_repo):
        """创建租户服务实例"""
        return TenantService(
            session=mock_session,
            redis_repo=mock_redis_repo,
            user_model=User,
            tenant_model=Tenant,
            role_model=Role,
            permission_model=Permission,
            user_role_model=UserRole,
            role_permission_model=RolePermission,
            audit_log_model=AuditLog
        )

    @pytest.fixture
    def sample_tenant_data(self):
        """示例租户数据"""
        return {
            "tenant_name": "测试企业",
            "tenant_code": "TEST_CORP",
            "description": "这是一个测试企业",
            "max_users": 500,
            "settings": {
                "session_timeout": 3600,
                "password_policy": {
                    "min_length": 8,
                    "require_special_char": True
                }
            }
        }

    @pytest.fixture
    def sample_admin_user(self):
        """示例管理员用户数据"""
        return {
            "username": "admin",
            "email": "<EMAIL>",
            "phone": "13800138000",
            "password": "Admin123!@#",
            "nickname": "管理员"
        }

    @pytest.mark.asyncio
    async def test_create_tenant_success(self, tenant_service, mock_session, mock_redis_repo, sample_tenant_data, sample_admin_user):
        """测试成功创建租户"""
        # 模拟数据库查询返回空（表示没有重复）
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        mock_session.commit = AsyncMock()
        mock_session.flush = AsyncMock()
        mock_session.add = MagicMock()

        # 执行创建租户
        result = await tenant_service.create_tenant(
            tenant_name=sample_tenant_data["tenant_name"],
            tenant_code=sample_tenant_data["tenant_code"],
            description=sample_tenant_data["description"],
            max_users=sample_tenant_data["max_users"],
            settings=sample_tenant_data["settings"],
            admin_user=sample_admin_user
        )

        # 验证结果
        assert "tenant_id" in result
        assert result["tenant_name"] == sample_tenant_data["tenant_name"]
        assert result["tenant_code"] == sample_tenant_data["tenant_code"].upper()
        assert result["status"] == CommonStatus.PENDING
        assert result["max_users"] == sample_tenant_data["max_users"]
        assert "admin_user" in result
        assert "default_roles_created" in result
        assert "default_permissions_created" in result

        # 验证数据库操作
        assert mock_session.add.called
        assert mock_session.flush.called
        assert mock_session.commit.called

        # 验证缓存操作
        mock_redis_repo.set.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_tenant_validation_error(self, tenant_service):
        """测试创建租户时的验证错误"""
        # 测试空租户名称
        with pytest.raises(ValidationError, match="租户名称不能为空"):
            await tenant_service.create_tenant(
                tenant_name="",
                tenant_code="TEST"
            )

        # 测试空租户编码
        with pytest.raises(ValidationError, match="租户编码不能为空"):
            await tenant_service.create_tenant(
                tenant_name="测试企业",
                tenant_code=""
            )

        # 测试无效的最大用户数
        with pytest.raises(ValidationError, match="最大用户数必须大于0"):
            await tenant_service.create_tenant(
                tenant_name="测试企业",
                tenant_code="TEST",
                max_users=0
            )

    @pytest.mark.asyncio
    async def test_create_tenant_duplicate_error(self, tenant_service, mock_session, sample_tenant_data):
        """测试创建重复租户"""
        # 模拟数据库查询返回已存在的租户
        existing_tenant = MagicMock()
        existing_tenant.tenant_code = sample_tenant_data["tenant_code"]
        mock_session.execute.return_value.scalar_one_or_none.return_value = existing_tenant

        # 测试重复租户编码
        with pytest.raises(DuplicateResourceError):
            await tenant_service.create_tenant(
                tenant_name=sample_tenant_data["tenant_name"],
                tenant_code=sample_tenant_data["tenant_code"]
            )

    @pytest.mark.asyncio
    async def test_list_tenants_success(self, tenant_service, mock_session):
        """测试成功获取租户列表"""
        # 模拟租户数据
        mock_tenant = MagicMock()
        mock_tenant.tenant_id = "tenant_123"
        mock_tenant.tenant_name = "测试企业"
        mock_tenant.tenant_code = "TEST"
        mock_tenant.description = "测试描述"
        mock_tenant.status = CommonStatus.ACTIVE
        mock_tenant.max_users = 1000
        mock_tenant.settings = {}
        mock_tenant.created_at = datetime.now()
        mock_tenant.updated_at = datetime.now()

        # 模拟数据库查询
        mock_session.execute.return_value.scalars.return_value.all.return_value = [mock_tenant]
        mock_session.execute.return_value.scalar.return_value = 5  # 用户数量

        result = await tenant_service.list_tenants(limit=10)

        # 验证结果
        assert "tenants" in result
        assert "total" in result
        assert "has_more" in result
        assert len(result["tenants"]) == 1
        assert result["tenants"][0]["tenant_id"] == "tenant_123"
        assert result["tenants"][0]["tenant_name"] == "测试企业"

    @pytest.mark.asyncio
    async def test_get_tenant_detail_success(self, tenant_service, mock_session, mock_redis_repo):
        """测试成功获取租户详情"""
        tenant_id = "tenant_123"
        
        # 模拟缓存未命中
        mock_redis_repo.get.return_value = None

        # 模拟租户数据
        mock_tenant = MagicMock()
        mock_tenant.tenant_id = tenant_id
        mock_tenant.tenant_name = "测试企业"
        mock_tenant.tenant_code = "TEST"
        mock_tenant.description = "测试描述"
        mock_tenant.status = CommonStatus.ACTIVE
        mock_tenant.max_users = 1000
        mock_tenant.settings = {}
        mock_tenant.created_at = datetime.now()
        mock_tenant.updated_at = datetime.now()

        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant
        mock_session.execute.return_value.scalar.return_value = 5  # 统计数据

        result = await tenant_service.get_tenant_detail(tenant_id)

        # 验证结果
        assert result["tenant_id"] == tenant_id
        assert result["tenant_name"] == "测试企业"
        assert "statistics" in result
        assert result["statistics"]["user_count"] == 5

        # 验证缓存操作
        mock_redis_repo.set.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_tenant_detail_not_found(self, tenant_service, mock_session, mock_redis_repo):
        """测试获取不存在的租户详情"""
        tenant_id = "nonexistent_tenant"
        
        # 模拟缓存未命中
        mock_redis_repo.get.return_value = None
        
        # 模拟数据库查询返回空
        mock_session.execute.return_value.scalar_one_or_none.return_value = None

        with pytest.raises(NotFoundError):
            await tenant_service.get_tenant_detail(tenant_id)

    @pytest.mark.asyncio
    async def test_update_tenant_success(self, tenant_service, mock_session, mock_redis_repo):
        """测试成功更新租户"""
        tenant_id = "tenant_123"
        
        # 模拟现有租户
        mock_tenant = MagicMock()
        mock_tenant.tenant_id = tenant_id
        mock_tenant.tenant_name = "旧名称"
        mock_tenant.description = "旧描述"
        mock_tenant.max_users = 500
        mock_tenant.settings = {}
        mock_tenant.status = CommonStatus.PENDING

        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant
        mock_session.commit = AsyncMock()

        # 执行更新
        result = await tenant_service.update_tenant(
            tenant_id=tenant_id,
            tenant_name="新名称",
            description="新描述",
            max_users=1000
        )

        # 验证结果
        assert result["tenant_id"] == tenant_id
        assert result["tenant_name"] == "新名称"
        assert "changes" in result

        # 验证数据库操作
        mock_session.commit.assert_called_once()
        
        # 验证缓存清理
        mock_redis_repo.delete.assert_called_with(f"tenant:{tenant_id}")

    @pytest.mark.asyncio
    async def test_delete_tenant_success(self, tenant_service, mock_session, mock_redis_repo):
        """测试成功删除租户"""
        tenant_id = "tenant_123"
        
        # 模拟现有租户
        mock_tenant = MagicMock()
        mock_tenant.tenant_id = tenant_id
        mock_tenant.tenant_name = "测试企业"

        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant
        mock_session.execute.return_value.scalar.return_value = 0  # 无依赖
        mock_session.execute.return_value.scalars.return_value.all.return_value = []  # 无相关数据
        mock_session.commit = AsyncMock()

        # 执行删除
        result = await tenant_service.delete_tenant(tenant_id)

        # 验证结果
        assert result["tenant_id"] == tenant_id
        assert result["status"] == CommonStatus.DELETED
        assert "cleanup_summary" in result

        # 验证数据库操作
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_tenant_with_dependencies(self, tenant_service, mock_session):
        """测试删除有依赖关系的租户"""
        tenant_id = "tenant_123"
        
        # 模拟现有租户
        mock_tenant = MagicMock()
        mock_tenant.tenant_id = tenant_id
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant
        
        # 模拟有活跃用户
        mock_session.execute.return_value.scalar.return_value = 5  # 5个活跃用户

        with pytest.raises(BusinessError, match="无法删除租户，存在依赖关系"):
            await tenant_service.delete_tenant(tenant_id, force_delete=False)
