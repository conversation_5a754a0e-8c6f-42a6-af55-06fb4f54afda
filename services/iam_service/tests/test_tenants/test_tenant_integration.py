"""
租户管理集成测试

测试租户管理的完整流程和API接口
"""

import pytest
import json
from httpx import AsyncClient
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch

from main import app
from services.tenant_service import TenantService
from domain_common.models.constants import CommonStatus


class TestTenantIntegration:
    """租户管理集成测试类"""

    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)

    @pytest.fixture
    def sample_create_request(self):
        """示例创建租户请求"""
        return {
            "meta": {
                "request_id": "req_123",
                "timestamp": "2025-01-23 10:30:45.123",
                "version": "v1"
            },
            "data": {
                "tenant_name": "测试企业",
                "tenant_code": "TEST_CORP",
                "description": "这是一个测试企业",
                "max_users": 500,
                "settings": {
                    "session_timeout": 3600,
                    "password_policy": {
                        "min_length": 8,
                        "require_special_char": True
                    }
                }
            }
        }

    @pytest.fixture
    def sample_list_request(self):
        """示例查询租户列表请求"""
        return {
            "meta": {
                "request_id": "req_124",
                "timestamp": "2025-01-23 10:30:45.123",
                "version": "v1"
            },
            "data": {
                "cursor": None,
                "limit": 20,
                "search": None,
                "status": None
            }
        }

    @pytest.fixture
    def sample_detail_request(self):
        """示例获取租户详情请求"""
        return {
            "meta": {
                "request_id": "req_125",
                "timestamp": "2025-01-23 10:30:45.123",
                "version": "v1"
            },
            "data": {
                "tenant_id": "tenant_123"
            }
        }

    @pytest.fixture
    def sample_update_request(self):
        """示例更新租户请求"""
        return {
            "meta": {
                "request_id": "req_126",
                "timestamp": "2025-01-23 10:30:45.123",
                "version": "v1"
            },
            "data": {
                "tenant_id": "tenant_123",
                "tenant_name": "更新后的企业名称",
                "description": "更新后的描述",
                "max_users": 1000
            }
        }

    @pytest.fixture
    def sample_delete_request(self):
        """示例删除租户请求"""
        return {
            "meta": {
                "request_id": "req_127",
                "timestamp": "2025-01-23 10:30:45.123",
                "version": "v1"
            },
            "data": {
                "tenant_id": "tenant_123"
            }
        }

    @patch('services.iam_service.services.tenant_service.TenantService.create_tenant')
    def test_create_tenant_api(self, mock_create_tenant, client, sample_create_request):
        """测试创建租户API"""
        # 模拟服务返回
        mock_create_tenant.return_value = {
            "tenant_id": "tenant_123",
            "tenant_name": "测试企业",
            "tenant_code": "TEST_CORP",
            "status": CommonStatus.PENDING,
            "max_users": 500,
            "current_users": 0,
            "settings": sample_create_request["data"]["settings"],
            "created_at": "2025-01-23T10:30:45",
            "updated_at": "2025-01-23T10:30:45",
            "default_roles_created": ["SUPER_ADMIN", "ADMIN", "USER"],
            "default_permissions_created": 9
        }

        # 发送请求
        response = client.post(
            "/tenants/create",
            json=sample_create_request,
            headers={"Content-Type": "application/json"}
        )

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["code"] == 200
        assert data["message"] == "租户创建成功"
        assert "data" in data
        assert data["data"]["tenant_id"] == "tenant_123"
        assert data["data"]["tenant_name"] == "测试企业"

    @patch('services.iam_service.services.tenant_service.TenantService.list_tenants')
    def test_list_tenants_api(self, mock_list_tenants, client, sample_list_request):
        """测试查询租户列表API"""
        # 模拟服务返回
        mock_list_tenants.return_value = {
            "tenants": [
                {
                    "tenant_id": "tenant_123",
                    "tenant_name": "测试企业",
                    "tenant_code": "TEST_CORP",
                    "description": "测试描述",
                    "status": CommonStatus.ACTIVE,
                    "max_users": 1000,
                    "current_users": 25,
                    "settings": {},
                    "created_at": "2025-01-23T10:30:45",
                    "updated_at": "2025-01-23T11:00:00"
                }
            ],
            "total": 1,
            "next_cursor": None,
            "has_more": False
        }

        # 发送请求
        response = client.post(
            "/tenants/list",
            json=sample_list_request,
            headers={"Content-Type": "application/json"}
        )

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["message"] == "查询成功"
        assert len(data["data"]["tenants"]) == 1
        assert data["data"]["total"] == 1

    @patch('services.iam_service.services.tenant_service.TenantService.get_tenant_detail')
    def test_get_tenant_detail_api(self, mock_get_tenant_detail, client, sample_detail_request):
        """测试获取租户详情API"""
        # 模拟服务返回
        mock_get_tenant_detail.return_value = {
            "tenant_id": "tenant_123",
            "tenant_name": "测试企业",
            "tenant_code": "TEST_CORP",
            "description": "测试描述",
            "status": CommonStatus.ACTIVE,
            "max_users": 1000,
            "current_users": 25,
            "settings": {
                "session_timeout": 3600
            },
            "statistics": {
                "user_count": 25,
                "active_user_count": 20,
                "role_count": 5,
                "permission_count": 15,
                "last_updated": "2025-01-23T10:30:45"
            },
            "created_at": "2025-01-23T10:30:45",
            "updated_at": "2025-01-23T11:00:00"
        }

        # 发送请求
        response = client.post(
            "/tenants/detail",
            json=sample_detail_request,
            headers={"Content-Type": "application/json"}
        )

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["data"]["tenant_id"] == "tenant_123"
        assert "statistics" in data["data"]

    @patch('services.iam_service.services.tenant_service.TenantService.update_tenant')
    def test_update_tenant_api(self, mock_update_tenant, client, sample_update_request):
        """测试更新租户API"""
        # 模拟服务返回
        mock_update_tenant.return_value = {
            "tenant_id": "tenant_123",
            "tenant_name": "更新后的企业名称",
            "tenant_code": "TEST_CORP",
            "description": "更新后的描述",
            "status": CommonStatus.ACTIVE,
            "max_users": 1000,
            "settings": {},
            "updated_at": "2025-01-23T10:35:00",
            "changes": {
                "tenant_name": {
                    "old": "测试企业",
                    "new": "更新后的企业名称"
                },
                "max_users": {
                    "old": 500,
                    "new": 1000
                }
            }
        }

        # 发送请求
        response = client.post(
            "/tenants/update",
            json=sample_update_request,
            headers={"Content-Type": "application/json"}
        )

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["message"] == "租户信息更新成功"
        assert data["data"]["tenant_name"] == "更新后的企业名称"
        assert "changes" in data["data"]

    @patch('services.iam_service.services.tenant_service.TenantService.delete_tenant')
    def test_delete_tenant_api(self, mock_delete_tenant, client, sample_delete_request):
        """测试删除租户API"""
        # 模拟服务返回
        mock_delete_tenant.return_value = {
            "tenant_id": "tenant_123",
            "delete_type": "soft",
            "status": CommonStatus.DELETED,
            "deleted_at": "2025-01-23T10:30:45",
            "cleanup_summary": {
                "users_archived": 25,
                "roles_removed": 5,
                "permissions_removed": 15
            }
        }

        # 发送请求
        response = client.post(
            "/tenants/delete",
            json=sample_delete_request,
            headers={"Content-Type": "application/json"}
        )

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["message"] == "租户删除成功"
        assert data["data"]["tenant_id"] == "tenant_123"
        assert data["data"]["status"] == CommonStatus.DELETED

    def test_create_tenant_validation_error(self, client):
        """测试创建租户时的验证错误"""
        invalid_request = {
            "meta": {
                "request_id": "req_123",
                "timestamp": "2025-01-23 10:30:45.123",
                "version": "v1"
            },
            "data": {
                "tenant_name": "",  # 空名称
                "tenant_code": "TEST"
            }
        }

        response = client.post(
            "/tenants/create",
            json=invalid_request,
            headers={"Content-Type": "application/json"}
        )

        # 验证响应
        assert response.status_code == 422  # 验证错误

    def test_invalid_request_format(self, client):
        """测试无效的请求格式"""
        invalid_request = {
            "invalid_field": "invalid_value"
        }

        response = client.post(
            "/tenants/create",
            json=invalid_request,
            headers={"Content-Type": "application/json"}
        )

        # 验证响应
        assert response.status_code == 422  # 验证错误

    @pytest.mark.asyncio
    async def test_tenant_lifecycle_flow(self):
        """测试租户完整生命周期流程"""
        # 这个测试需要真实的数据库连接，通常在集成测试环境中运行
        # 1. 创建租户
        # 2. 查询租户列表
        # 3. 获取租户详情
        # 4. 更新租户信息
        # 5. 删除租户
        
        # 由于需要真实的数据库和Redis连接，这里只是示例结构
        # 在实际测试中，需要设置测试数据库和测试环境
        pass
