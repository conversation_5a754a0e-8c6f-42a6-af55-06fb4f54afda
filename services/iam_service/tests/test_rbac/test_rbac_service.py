"""
RBAC服务测试

测试RBAC服务的各项功能
"""

import pytest
import uuid
from datetime import datetime, date
from unittest.mock import AsyncMock, MagicMock

from services.rbac_service import RBACService
from domain_common.models import CommonStatus
from domain_common.models.iam_models import (
    User, Tenant, Role, Permission,
    UserRole, RolePermission, AuditLog
)
from commonlib.exceptions.exceptions import (
    ValidationError, DuplicateResourceError, NotFoundError,
    BusinessError
)


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    session = AsyncMock()
    return session


@pytest.fixture
def mock_redis_repo():
    """模拟Redis仓库"""
    redis_repo = AsyncMock()
    return redis_repo


@pytest.fixture
def rbac_service(mock_session, mock_redis_repo):
    """创建RBAC服务实例"""
    return RBACService(
        session=mock_session,
        redis_repo=mock_redis_repo,
        user_model=User,
        tenant_model=Tenant,
        role_model=Role,
        permission_model=Permission,
        user_role_model=UserRole,
        role_permission_model=RolePermission,
        audit_log_model=AuditLog
    )


class TestRBACService:
    """RBAC服务测试类"""

    @pytest.mark.asyncio
    async def test_create_role_success(self, rbac_service, mock_session):
        """测试成功创建角色"""
        # 准备测试数据
        tenant_id = "tenant_123"
        role_name = "测试角色"
        role_code = "TEST_ROLE"
        
        # 模拟租户存在
        mock_tenant = MagicMock()
        mock_tenant.status = CommonStatus.ACTIVE
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant
        
        # 模拟角色代码不存在（唯一性检查）
        mock_session.execute.return_value.scalar_one_or_none.side_effect = [
            mock_tenant,  # 租户查询
            None,  # 角色代码唯一性检查
        ]
        
        # 执行测试
        result = await rbac_service.create_role(
            tenant_id=tenant_id,
            role_name=role_name,
            role_code=role_code,
            description="测试角色描述",
            level=1,
            is_system=False,
            permissions=["user:read", "user:write"]
        )
        
        # 验证结果
        assert result["role_name"] == role_name
        assert result["role_code"] == role_code
        assert "role_id" in result
        assert "created_at" in result
        assert result["permissions_assigned"] >= 0
        
        # 验证数据库操作
        mock_session.add.assert_called()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_role_duplicate_code(self, rbac_service, mock_session):
        """测试创建重复角色代码"""
        # 准备测试数据
        tenant_id = "tenant_123"
        role_code = "EXISTING_ROLE"
        
        # 模拟租户存在
        mock_tenant = MagicMock()
        mock_tenant.status = CommonStatus.ACTIVE
        
        # 模拟角色代码已存在
        mock_existing_role = MagicMock()
        mock_session.execute.return_value.scalar_one_or_none.side_effect = [
            mock_tenant,  # 租户查询
            mock_existing_role,  # 角色代码已存在
        ]
        
        # 执行测试并验证异常
        with pytest.raises(DuplicateResourceError, match="角色代码已存在"):
            await rbac_service.create_role(
                tenant_id=tenant_id,
                role_name="测试角色",
                role_code=role_code
            )

    @pytest.mark.asyncio
    async def test_create_permission_success(self, rbac_service, mock_session):
        """测试成功创建权限"""
        # 准备测试数据
        tenant_id = "tenant_123"
        permission_code = "user:read"
        permission_name = "查看用户"
        
        # 模拟权限代码不存在（唯一性检查）
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        
        # 执行测试
        result = await rbac_service.create_permission(
            tenant_id=tenant_id,
            permission_code=permission_code,
            permission_name=permission_name,
            description="查看用户权限",
            resource_type="user",
            action="read",
            category="business",
            is_system=False
        )
        
        # 验证结果
        assert result["permission_code"] == permission_code
        assert result["permission_name"] == permission_name
        assert "permission_id" in result
        assert "created_at" in result
        
        # 验证数据库操作
        mock_session.add.assert_called()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_query_roles_with_pagination(self, rbac_service, mock_session):
        """测试分页查询角色"""
        # 准备测试数据
        tenant_id = "tenant_123"
        
        # 模拟查询结果
        mock_roles = [
            MagicMock(
                role_id="role_1",
                role_name="角色1",
                role_code="ROLE_1",
                description="角色1描述",
                parent_role_id=None,
                level=1,
                status=CommonStatus.ACTIVE,
                meta_data={"is_system": False},
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),
            MagicMock(
                role_id="role_2",
                role_name="角色2",
                role_code="ROLE_2",
                description="角色2描述",
                parent_role_id=None,
                level=2,
                status=CommonStatus.ACTIVE,
                meta_data={"is_system": True},
                created_at=datetime.utcnow(),
                updated_at=None
            )
        ]
        
        # 模拟数据库查询
        mock_session.execute.return_value.scalar.return_value = 2  # 总数
        mock_session.execute.return_value.scalars.return_value.all.return_value = mock_roles
        
        # 执行测试
        result = await rbac_service.query_roles(
            tenant_id=tenant_id,
            page=1,
            page_size=10,
            include_permissions=False,
            include_users=False
        )
        
        # 验证结果
        assert result["total"] == 2
        assert len(result["roles"]) == 2
        assert result["page"] == 1
        assert result["page_size"] == 10
        assert result["has_next"] is False
        
        # 验证角色数据
        role1 = result["roles"][0]
        assert role1["role_id"] == "role_1"
        assert role1["role_name"] == "角色1"
        assert role1["is_system"] is False
        
        role2 = result["roles"][1]
        assert role2["role_id"] == "role_2"
        assert role2["is_system"] is True

    @pytest.mark.asyncio
    async def test_check_permission_success(self, rbac_service, mock_session):
        """测试权限检查成功"""
        # 准备测试数据
        tenant_id = "tenant_123"
        user_id = "user_123"
        permission_code = "user:read"
        
        # 模拟缓存未命中
        rbac_service.redis_repo.get.return_value = None
        
        # 模拟用户权限查询结果
        mock_permissions = [("user:read", "管理员"), ("user:write", "管理员")]
        mock_session.execute.return_value.fetchall.return_value = mock_permissions
        
        # 执行测试
        result = await rbac_service.check_permission(
            tenant_id=tenant_id,
            user_id=user_id,
            permission_code=permission_code
        )
        
        # 验证结果
        assert result["user_id"] == user_id
        assert result["permission_code"] == permission_code
        assert result["has_permission"] is True
        assert result["permission_source"] == "管理员"
        assert result["cache_hit"] is False
        assert "checked_at" in result

    @pytest.mark.asyncio
    async def test_assign_user_roles_success(self, rbac_service, mock_session):
        """测试分配用户角色成功"""
        # 准备测试数据
        tenant_id = "tenant_123"
        user_id = "user_123"
        role_ids = ["role_1", "role_2"]
        
        # 模拟用户存在
        mock_user = MagicMock()
        mock_user.tenant_id = tenant_id
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_user
        
        # 模拟角色验证成功
        rbac_service._validate_roles_exist = AsyncMock(return_value=role_ids)
        rbac_service._add_roles_to_user = AsyncMock(return_value=2)
        rbac_service._get_user_roles = AsyncMock(return_value=[
            {"role_id": "role_1", "role_name": "角色1"},
            {"role_id": "role_2", "role_name": "角色2"}
        ])
        
        # 执行测试
        result = await rbac_service.assign_user_roles(
            tenant_id=tenant_id,
            user_id=user_id,
            role_ids=role_ids,
            operation="assign"
        )
        
        # 验证结果
        assert result["user_id"] == user_id
        assert result["operation"] == "assign"
        assert result["affected_roles"] == 2
        assert len(result["current_roles"]) == 2
        assert "operation_time" in result
        
        # 验证数据库操作
        mock_session.commit.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
