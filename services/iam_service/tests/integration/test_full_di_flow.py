"""
Full DI Flow测试

测试完整的依赖注入流程，从配置加载到服务使用
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch
import tempfile
import json
import os

from commonlib.core.containers.config_container import set_up_config_di
from commonlib.core.containers.infra_container import InfraContainer
from container import ServiceContainer
from domain_common.app_builder.default_app_factory import AppInitializer


class TestFullDIFlow:
    """Full DI Flow测试类"""

    @pytest.mark.integration
    @patch('commonlib.core.containers.config_container.load_dotenv')
    @patch('commonlib.core.logging.tsif_logging.app_logger.initialize')
    def test_complete_application_startup_flow(self, mock_logger_init, mock_load_dotenv):
        """测试完整的应用启动流程"""
        # 创建临时配置文件
        config_data = {
            "application": {
                "project_name": "test_iam_service",
                "debug": True,
                "title": "Test IAM Service"
            },
            "persistence": {
                "redis": {"host": "localhost", "port": 6379, "db": 15},
                "postgres": {"host": "localhost", "port": 5432, "database": "test_iam"}
            },
            "connection_priority": {"redis": 1, "postgres": 2},
            "log_dir": "/tmp/test_logs"
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            config_path = f.name
        
        try:
            # Mock配置加载
            with patch('commonlib.configs.config_loader.ConfigLoader') as mock_loader_class:
                mock_loader = MagicMock()
                mock_config_obj = MagicMock()
                
                # 设置配置对象属性
                mock_config_obj.application.project_name = "test_iam_service"
                mock_config_obj.debug = True
                mock_config_obj.log_dir = "/tmp/test_logs"
                mock_config_obj.persistence = MagicMock()
                mock_config_obj.connection_priority = MagicMock()
                
                mock_loader.get_config.return_value = mock_config_obj
                mock_loader_class.return_value = mock_loader
                
                # 1. 初始化配置容器
                config = set_up_config_di(config_path=config_path)
                
                # 2. 创建基础设施容器
                infra = InfraContainer(config=config)
                
                # Mock连接管理器
                mock_connection_manager = MagicMock()
                mock_redis_client = AsyncMock()
                mock_postgres_client = AsyncMock()
                
                mock_connection_manager.get_connector.side_effect = lambda name: {
                    'redis': mock_redis_client,
                    'postgres': mock_postgres_client
                }.get(name)
                
                infra.connection_manager.override(mock_connection_manager)
                
                # 3. 创建服务容器
                services = ServiceContainer(config=config, infra=infra)
                
                # 4. 创建应用初始化器
                wire_modules = ["routes.tenants", "services.tenant_service"]
                app_initializer = AppInitializer(
                    config=config,
                    infra=infra,
                    services=services,
                    wire_modules=wire_modules
                )
                
                # 验证完整流程
                assert config is not None
                assert infra is not None
                assert services is not None
                assert app_initializer is not None
                
        finally:
            # 清理临时文件
            os.unlink(config_path)

    @pytest.mark.integration
    def test_service_dependency_chain_resolution(self):
        """测试服务依赖链解析"""
        # Mock配置
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_iam_service"
        
        # 创建容器链
        infra = InfraContainer(config=mock_config)
        services = ServiceContainer(config=mock_config, infra=infra)
        
        # Mock基础依赖
        mock_redis_client = AsyncMock()
        mock_postgres_session = AsyncMock()
        mock_connection_manager = MagicMock()
        
        mock_connection_manager.get_connector.side_effect = lambda name: {
            'redis': mock_redis_client,
            'postgres': mock_postgres_session
        }.get(name)
        
        infra.connection_manager.override(mock_connection_manager)
        infra.postgres_session_provider.override(lambda: mock_postgres_session)
        
        # 测试依赖链：connection_manager -> redis_client -> redis_repo -> tenant_service
        with patch('commonlib.storages.persistence.redis.repository.RedisRepository') as mock_repo_class, \
             patch('services.tenant_service.TenantService') as mock_service_class:
            
            mock_redis_repo = AsyncMock()
            mock_tenant_service = MagicMock()
            
            mock_repo_class.return_value = mock_redis_repo
            mock_service_class.return_value = mock_tenant_service
            
            # 获取租户服务（触发整个依赖链）
            tenant_service = services.tenant_service()
            
            # 验证依赖链正确解析
            mock_connection_manager.get_connector.assert_called_with("redis")
            mock_repo_class.assert_called_once()
            mock_service_class.assert_called_once()

    @pytest.mark.integration
    def test_security_components_integration(self):
        """测试安全组件集成"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        services = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock Redis仓库
        mock_redis_repo = AsyncMock()
        services.redis_repo.override(mock_redis_repo)
        
        # Mock安全配置
        with patch('container.security_config_manager') as mock_security_config:
            # JWT配置
            mock_security_config.jwt_config.private_key = "test_private_key"
            mock_security_config.jwt_config.public_key = "test_public_key"
            mock_security_config.jwt_config.access_token_expire_minutes = 30
            mock_security_config.jwt_config.refresh_token_expire_days = 7
            mock_security_config.jwt_config.algorithm = "RS256"
            
            # 会话配置
            mock_security_config.session_config.session_timeout_minutes = 60
            mock_security_config.session_config.max_concurrent_sessions = 5
            mock_security_config.session_config.enable_device_tracking = True
            
            # 缓存配置
            mock_security_config.cache_config = MagicMock()
            
            # 测试安全组件创建
            with patch('security.jwt_manager.JWTManager') as mock_jwt_class, \
                 patch('security.session_manager.SessionManager') as mock_session_class, \
                 patch('security.cache_manager.CacheManager') as mock_cache_class, \
                 patch('security.security_utils.SecurityUtils') as mock_utils_class:
                
                mock_jwt_manager = MagicMock()
                mock_session_manager = AsyncMock()
                mock_cache_manager = AsyncMock()
                mock_security_utils = MagicMock()
                
                mock_jwt_class.return_value = mock_jwt_manager
                mock_session_class.return_value = mock_session_manager
                mock_cache_class.return_value = mock_cache_manager
                mock_utils_class.return_value = mock_security_utils
                
                # 创建安全组件
                jwt_manager = services.jwt_manager()
                session_manager = services.session_manager()
                cache_manager = services.cache_manager()
                security_utils = services.security_utils()
                
                # 验证安全组件正确创建
                assert jwt_manager is mock_jwt_manager
                assert session_manager is mock_session_manager
                assert cache_manager is mock_cache_manager
                assert security_utils is mock_security_utils

    @pytest.mark.integration
    def test_external_services_integration(self):
        """测试外部服务集成"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        services = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock Redis仓库
        mock_redis_repo = AsyncMock()
        services.redis_repo.override(mock_redis_repo)
        
        # 测试外部服务创建
        with patch('external_services.sms_service.MockSMSProvider') as mock_sms_provider_class, \
             patch('external_services.sms_service.SMSService') as mock_sms_service_class, \
             patch('external_services.email_service.EmailService') as mock_email_service_class, \
             patch('external_services.verification_service.VerificationService') as mock_verification_service_class:
            
            mock_sms_provider = MagicMock()
            mock_sms_service = AsyncMock()
            mock_email_service = AsyncMock()
            mock_verification_service = AsyncMock()
            
            mock_sms_provider_class.return_value = mock_sms_provider
            mock_sms_service_class.return_value = mock_sms_service
            mock_email_service_class.return_value = mock_email_service
            mock_verification_service_class.return_value = mock_verification_service
            
            # 创建外部服务
            sms_provider = services.sms_provider()
            sms_service = services.sms_service()
            email_service = services.email_service()
            verification_service = services.verification_service()
            
            # 验证外部服务正确创建
            assert sms_provider is mock_sms_provider
            assert sms_service is mock_sms_service
            assert email_service is mock_email_service
            assert verification_service is mock_verification_service

    @pytest.mark.integration
    def test_business_services_integration(self):
        """测试业务服务集成"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        services = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # Mock所有必要的依赖
        mock_session = AsyncMock()
        mock_redis_repo = AsyncMock()
        mock_jwt_manager = MagicMock()
        mock_session_manager = AsyncMock()
        mock_security_utils = MagicMock()
        
        services.session.override(mock_session)
        services.redis_repo.override(mock_redis_repo)
        services.jwt_manager.override(mock_jwt_manager)
        services.session_manager.override(mock_session_manager)
        services.security_utils.override(mock_security_utils)
        
        # 测试业务服务创建
        with patch('services.tenant_service.TenantService') as mock_tenant_class, \
             patch('services.user_service.UserService') as mock_user_class, \
             patch('services.auth_service.AuthService') as mock_auth_class:
            
            mock_tenant_service = MagicMock()
            mock_user_service = MagicMock()
            mock_auth_service = MagicMock()
            
            mock_tenant_class.return_value = mock_tenant_service
            mock_user_class.return_value = mock_user_service
            mock_auth_class.return_value = mock_auth_service
            
            # 创建业务服务
            tenant_service = services.tenant_service()
            user_service = services.user_service()
            auth_service = services.auth_service()
            
            # 验证业务服务正确创建
            assert tenant_service is mock_tenant_service
            assert user_service is mock_user_service
            assert auth_service is mock_auth_service

    @pytest.mark.integration
    def test_container_wiring_and_injection_flow(self):
        """测试容器wiring和注入流程"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_iam_service"
        
        # 创建容器
        infra = InfraContainer(config=mock_config)
        services = ServiceContainer(config=mock_config, infra=infra)
        
        # Mock依赖
        mock_redis_repo = AsyncMock()
        services.redis_repo.override(mock_redis_repo)
        
        # 定义使用依赖注入的测试函数
        from dependency_injector.wiring import inject, Provide
        
        @inject
        def test_injected_function(
            redis_repo = Provide[ServiceContainer.redis_repo]
        ):
            return redis_repo
        
        # Mock wire过程
        with patch.object(services, 'wire') as mock_wire:
            # Wire容器
            services.wire(modules=[__name__])
            mock_wire.assert_called_once_with(modules=[__name__])
            
            # 测试依赖注入（在实际环境中需要真正的wire）
            # 这里我们直接验证provider可以被调用
            redis_repo = services.redis_repo()
            assert redis_repo is mock_redis_repo

    @pytest.mark.integration
    def test_error_handling_in_di_flow(self):
        """测试DI流程中的错误处理"""
        mock_config = MagicMock()
        
        # 测试基础设施容器创建失败
        with patch.object(InfraContainer, '__init__') as mock_init:
            mock_init.side_effect = Exception("Infra container initialization failed")
            
            with pytest.raises(Exception, match="Infra container initialization failed"):
                InfraContainer(config=mock_config)
        
        # 测试服务容器创建失败
        mock_infra = MagicMock()
        with patch.object(ServiceContainer, '__init__') as mock_init:
            mock_init.side_effect = Exception("Service container initialization failed")
            
            with pytest.raises(Exception, match="Service container initialization failed"):
                ServiceContainer(config=mock_config, infra=mock_infra)

    @pytest.mark.integration
    def test_resource_cleanup_flow(self):
        """测试资源清理流程"""
        mock_config = MagicMock()
        
        # 创建容器
        infra = InfraContainer(config=mock_config)
        services = ServiceContainer(config=mock_config, infra=infra)
        
        # Mock资源方法
        with patch.object(infra, 'init_resources') as mock_infra_init, \
             patch.object(infra, 'shutdown_resources') as mock_infra_shutdown, \
             patch.object(services, 'init_resources') as mock_services_init, \
             patch.object(services, 'shutdown_resources') as mock_services_shutdown:
            
            # 模拟应用生命周期
            try:
                # 初始化资源
                infra.init_resources()
                services.init_resources()
                
                # 验证初始化被调用
                mock_infra_init.assert_called_once()
                mock_services_init.assert_called_once()
                
                # 模拟应用运行...
                
            finally:
                # 清理资源
                services.shutdown_resources()
                infra.shutdown_resources()
                
                # 验证清理被调用
                mock_services_shutdown.assert_called_once()
                mock_infra_shutdown.assert_called_once()
