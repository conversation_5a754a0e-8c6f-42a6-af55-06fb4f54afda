"""
Container Integration测试

测试容器间的集成和完整的DI流程
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from commonlib.core.containers.config_container import ConfigContainer, set_up_config_di
from commonlib.core.containers.infra_container import InfraContainer
from container import ServiceContainer
from domain_common.app_builder.default_app_factory import AppInitializer


class TestContainerIntegration:
    """Container Integration测试类"""

    @pytest.mark.integration
    @patch('commonlib.core.containers.config_container.load_dotenv')
    @patch('commonlib.core.logging.tsif_logging.app_logger.initialize')
    def test_full_container_initialization_chain(self, mock_logger_init, mock_load_dotenv):
        """测试完整的容器初始化链"""
        # Mock配置
        mock_config_data = MagicMock()
        mock_config_data.application.project_name = "test_iam_service"
        mock_config_data.debug = True
        mock_config_data.log_dir = "/tmp/test_logs"
        mock_config_data.persistence = MagicMock()
        mock_config_data.connection_priority = MagicMock()
        
        # 创建配置容器
        config_container = ConfigContainer()
        mock_loader = MagicMock()
        mock_loader.get_config.return_value = mock_config_data
        config_container.config_loader.override(mock_loader)
        config_container.init_resources()
        
        # 创建基础设施容器
        infra_container = InfraContainer(config=config_container)
        
        # Mock连接管理器
        mock_connection_manager = MagicMock()
        infra_container.connection_manager.override(mock_connection_manager)
        
        # 创建服务容器
        service_container = ServiceContainer(
            config=config_container,
            infra=infra_container
        )
        
        # 验证容器链正确创建
        assert service_container.config is config_container
        assert service_container.infra is infra_container
        assert isinstance(service_container, ServiceContainer)

    @pytest.mark.integration
    def test_cross_container_dependency_resolution(self):
        """测试跨容器依赖解析"""
        # Mock配置容器
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_iam_service"
        
        # 创建基础设施容器
        infra_container = InfraContainer(config=mock_config)
        
        # Mock基础设施依赖
        mock_redis_client = AsyncMock()
        mock_postgres_session_provider = AsyncMock()
        
        infra_container.redis_clint.override(mock_redis_client)
        infra_container.postgres_session_provider.override(mock_postgres_session_provider)
        
        # 创建服务容器
        service_container = ServiceContainer(
            config=mock_config,
            infra=infra_container
        )
        
        # 测试跨容器依赖解析
        # Redis仓库应该使用infra容器的redis_clint
        redis_repo_provider = service_container.redis_repo
        assert redis_repo_provider is not None
        
        # 会话应该使用infra容器的postgres_session_provider
        session_provider = service_container.session
        assert session_provider is not None

    @pytest.mark.integration
    def test_service_instantiation_with_dependencies(self):
        """测试带依赖的服务实例化"""
        # Mock所有容器
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_iam_service"
        
        mock_infra = MagicMock()
        mock_infra.redis_clint = AsyncMock()
        mock_infra.postgres_session_provider.return_value = AsyncMock()
        
        service_container = ServiceContainer(
            config=mock_config,
            infra=mock_infra
        )
        
        # Mock所有必要的依赖
        mock_session = AsyncMock()
        mock_redis_repo = AsyncMock()
        mock_security_utils = MagicMock()
        
        service_container.session.override(mock_session)
        service_container.redis_repo.override(mock_redis_repo)
        service_container.security_utils.override(mock_security_utils)
        
        # 测试服务实例化
        with patch('services.tenant_service.TenantService') as mock_tenant_service_class:
            mock_tenant_service_instance = MagicMock()
            mock_tenant_service_class.return_value = mock_tenant_service_instance
            
            tenant_service = service_container.tenant_service()
            
            # 验证服务被正确实例化
            assert tenant_service is mock_tenant_service_instance
            mock_tenant_service_class.assert_called_once()

    @pytest.mark.integration
    def test_container_wiring_integration(self):
        """测试容器wiring集成"""
        # Mock配置
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_iam_service"
        
        # 创建容器
        infra_container = InfraContainer(config=mock_config)
        service_container = ServiceContainer(
            config=mock_config,
            infra=infra_container
        )
        
        # 定义wire模块
        infra_modules = ["domain_common.interface.infra_redis.rd_decorator"]
        service_modules = ["services.tenant_service", "services.user_service"]
        
        # Mock wire方法
        with patch.object(infra_container, 'wire') as mock_infra_wire, \
             patch.object(service_container, 'wire') as mock_service_wire:
            
            # 执行wiring
            infra_container.wire(modules=infra_modules)
            service_container.wire(modules=service_modules)
            
            # 验证wiring被正确执行
            mock_infra_wire.assert_called_once_with(modules=infra_modules)
            mock_service_wire.assert_called_once_with(modules=service_modules)

    @pytest.mark.integration
    def test_app_initializer_integration(self):
        """测试AppInitializer集成"""
        # Mock所有容器
        mock_config = MagicMock()
        mock_infra = MagicMock()
        mock_services = MagicMock()
        
        # Mock wire模块
        wire_modules = ["routes.tenants", "services.tenant_service"]
        
        # 创建AppInitializer
        app_initializer = AppInitializer(
            config=mock_config,
            infra=mock_infra,
            services=mock_services,
            wire_modules=wire_modules
        )
        
        # 验证AppInitializer正确初始化
        assert app_initializer._config is mock_config
        assert app_initializer._infra is mock_infra
        assert app_initializer._services is mock_services
        assert app_initializer._wire_modules == wire_modules

    @pytest.mark.integration
    @patch('commonlib.core.containers.config_container.load_dotenv')
    @patch('commonlib.core.logging.tsif_logging.app_logger.initialize')
    def test_complete_di_setup_flow(self, mock_logger_init, mock_load_dotenv):
        """测试完整的DI设置流程"""
        # 模拟完整的设置流程
        with patch('commonlib.configs.config_loader.ConfigLoader') as mock_loader_class:
            mock_loader = MagicMock()
            mock_config_data = MagicMock()
            mock_config_data.application.project_name = "test_iam_service"
            mock_config_data.debug = True
            mock_config_data.log_dir = "/tmp/test_logs"
            mock_config_data.persistence = MagicMock()
            mock_config_data.connection_priority = MagicMock()
            
            mock_loader.get_config.return_value = mock_config_data
            mock_loader_class.return_value = mock_loader
            
            # 设置配置DI
            config = set_up_config_di()
            
            # 创建基础设施容器
            infra = InfraContainer(config=config)
            
            # Mock连接管理器
            mock_connection_manager = MagicMock()
            infra.connection_manager.override(mock_connection_manager)
            
            # 创建服务容器
            services = ServiceContainer(config=config, infra=infra)
            
            # 验证完整流程
            assert isinstance(config, ConfigContainer)
            assert isinstance(infra, InfraContainer)
            assert isinstance(services, ServiceContainer)

    @pytest.mark.integration
    def test_container_resource_lifecycle(self):
        """测试容器资源生命周期"""
        # Mock配置
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_iam_service"
        
        # 创建容器
        infra_container = InfraContainer(config=mock_config)
        service_container = ServiceContainer(
            config=mock_config,
            infra=infra_container
        )
        
        # Mock资源初始化和关闭
        with patch.object(infra_container, 'init_resources') as mock_infra_init, \
             patch.object(infra_container, 'shutdown_resources') as mock_infra_shutdown, \
             patch.object(service_container, 'init_resources') as mock_service_init, \
             patch.object(service_container, 'shutdown_resources') as mock_service_shutdown:
            
            # 测试资源初始化
            infra_container.init_resources()
            service_container.init_resources()
            
            # 验证初始化被调用
            mock_infra_init.assert_called_once()
            mock_service_init.assert_called_once()
            
            # 测试资源关闭
            service_container.shutdown_resources()
            infra_container.shutdown_resources()
            
            # 验证关闭被调用
            mock_service_shutdown.assert_called_once()
            mock_infra_shutdown.assert_called_once()

    @pytest.mark.integration
    def test_container_error_propagation(self):
        """测试容器错误传播"""
        # Mock配置容器抛出异常
        mock_config = MagicMock()
        mock_config.app_name.provider.side_effect = Exception("Config error")
        
        # 测试错误传播到基础设施容器
        with pytest.raises(Exception):
            infra_container = InfraContainer(config=mock_config)
            # 尝试使用会导致错误的配置
            infra_container.decorator_redis_repo()

    @pytest.mark.integration
    def test_container_override_propagation(self):
        """测试容器override传播"""
        # Mock配置
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_iam_service"
        
        # 创建容器链
        infra_container = InfraContainer(config=mock_config)
        service_container = ServiceContainer(
            config=mock_config,
            infra=infra_container
        )
        
        # Override基础设施容器的依赖
        mock_redis_client = AsyncMock()
        infra_container.redis_clint.override(mock_redis_client)
        
        # 测试override传播到服务容器
        with patch('commonlib.storages.persistence.redis.repository.RedisRepository') as mock_repo_class:
            mock_repo_instance = AsyncMock()
            mock_repo_class.return_value = mock_repo_instance
            
            redis_repo = service_container.redis_repo()
            
            # 验证override的依赖被使用
            mock_repo_class.assert_called_once_with(
                key_prefix="test_iam_service",
                redis_connector=mock_redis_client
            )

    @pytest.mark.integration
    def test_multiple_service_container_isolation(self):
        """测试多个服务容器的隔离"""
        # 创建共享的基础设施容器
        mock_config = MagicMock()
        mock_config.app_name.provider = "shared_app"
        
        infra_container = InfraContainer(config=mock_config)
        
        # 创建两个独立的服务容器
        service_container1 = ServiceContainer(
            config=mock_config,
            infra=infra_container
        )
        service_container2 = ServiceContainer(
            config=mock_config,
            infra=infra_container
        )
        
        # Override不同的依赖
        mock_session1 = AsyncMock()
        mock_session2 = AsyncMock()
        
        service_container1.session.override(mock_session1)
        service_container2.session.override(mock_session2)
        
        # 验证容器隔离
        session1 = service_container1.session()
        session2 = service_container2.session()
        
        assert session1 is mock_session1
        assert session2 is mock_session2
        assert session1 is not session2
