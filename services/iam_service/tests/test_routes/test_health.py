"""
健康检查路由测试
"""

import pytest
from fastapi.testclient import TestClient


def test_health_check(client: TestClient):
    """测试健康检查接口"""
    response = client.get("/api/v1/health/")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "success"
    assert data["code"] == 200
    assert "service" in data["data"]
    assert data["data"]["service"] == "iam-service"
    assert data["data"]["status"] == "healthy"


def test_detailed_health_check(client: TestClient, mock_redis_repo):
    """测试详细健康检查接口"""
    # 模拟 Redis 连接正常
    mock_redis_repo.ping.return_value = True
    
    response = client.get("/api/v1/health/detailed")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "success"
    assert data["code"] == 200
    assert "dependencies" in data["data"]
    assert "redis" in data["data"]["dependencies"]
