#!/usr/bin/env python3
"""
测试运行脚本

提供不同类型测试的运行命令
"""

import subprocess
import sys
import argparse
from pathlib import Path


def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"运行: {description}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("警告:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        print(f"输出: {e.stdout}")
        print(f"错误: {e.stderr}")
        return False


def run_unit_tests():
    """运行单元测试"""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_security/test_jwt_manager.py",
        "-m", "not integration and not performance",
        "-v"
    ]
    return run_command(cmd, "JWT管理器单元测试")


def run_integration_tests():
    """运行集成测试"""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_security/test_jwt_integration.py",
        "-m", "integration",
        "-v", "-s"
    ]
    return run_command(cmd, "JWT管理器集成测试")


def run_performance_tests():
    """运行性能测试"""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_security/test_jwt_performance.py",
        "-m", "performance",
        "-v", "-s"
    ]
    return run_command(cmd, "JWT管理器性能测试")


def run_security_tests():
    """运行安全测试"""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_security/",
        "-m", "security",
        "-v"
    ]
    return run_command(cmd, "安全测试")


def run_all_tests():
    """运行所有测试"""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_security/",
        "-v"
    ]
    return run_command(cmd, "所有JWT相关测试")


def run_coverage_report():
    """生成覆盖率报告"""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_security/test_jwt_manager.py",
        "--cov=security.jwt_manager",
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing"
    ]
    return run_command(cmd, "代码覆盖率测试")


def check_dependencies():
    """检查测试依赖"""
    required_packages = [
        "pytest",
        "pytest-asyncio",
        "pytest-cov",
        "pytest-mock",
        "psutil"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"缺少以下测试依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("所有测试依赖已安装")
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="JWT管理器测试运行器")
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "performance", "security", "all", "coverage", "deps"],
        help="测试类型"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    
    args = parser.parse_args()
    
    # 切换到正确的目录
    script_dir = Path(__file__).parent
    original_dir = Path.cwd()
    
    try:
        import os
        os.chdir(script_dir)
        
        if args.test_type == "deps":
            success = check_dependencies()
        elif args.test_type == "unit":
            success = run_unit_tests()
        elif args.test_type == "integration":
            success = run_integration_tests()
        elif args.test_type == "performance":
            success = run_performance_tests()
        elif args.test_type == "security":
            success = run_security_tests()
        elif args.test_type == "all":
            success = run_all_tests()
        elif args.test_type == "coverage":
            success = run_coverage_report()
        else:
            print(f"未知的测试类型: {args.test_type}")
            success = False
        
        if success:
            print(f"\n✅ {args.test_type} 测试完成")
        else:
            print(f"\n❌ {args.test_type} 测试失败")
            sys.exit(1)
            
    finally:
        os.chdir(original_dir)


if __name__ == "__main__":
    main()
