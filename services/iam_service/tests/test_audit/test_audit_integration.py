"""
审计服务集成测试

测试审计服务与路由的集成
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock

from services.iam_service.services.audit_service import AuditService
from domain_common.models.iam_models import User, Tenant, AuditLog, AuditLogBuilder
from commonlib.exceptions.exceptions import ValidationError, BusinessError


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    session = AsyncMock()
    return session


@pytest.fixture
def mock_redis_repo():
    """模拟Redis仓库"""
    redis_repo = AsyncMock()
    return redis_repo


@pytest.fixture
def audit_service(mock_session, mock_redis_repo):
    """创建审计服务实例"""
    return AuditService(
        session=mock_session,
        redis_repo=mock_redis_repo,
        user_model=User,
        tenant_model=Tenant,
        audit_log_model=AuditLog
    )


class TestAuditServiceIntegration:
    """审计服务集成测试类"""

    @pytest.mark.asyncio
    async def test_complete_audit_workflow(self, audit_service, mock_session, mock_redis_repo):
        """测试完整的审计工作流程"""
        tenant_id = "tenant_123"
        
        # 1. 创建审计日志
        audit_service._clear_stats_cache = AsyncMock()
        audit_service._send_security_alert = AsyncMock()
        
        create_result = await audit_service.create_audit_log(
            tenant_id=tenant_id,
            user_id="user_123",
            action="LOGIN",
            resource_type="USER",
            description="用户登录系统",
            ip_address="*************",
            status="success"
        )
        
        assert "log_id" in create_result
        assert "created_at" in create_result
        mock_session.add.assert_called()
        mock_session.commit.assert_called()
        
        # 2. 查询审计日志
        mock_log = MagicMock()
        mock_log.id = 1
        mock_log.tenant_id = tenant_id
        mock_log.user_id = "user_123"
        mock_log.action = "LOGIN"
        mock_log.resource_type = "USER"
        mock_log.resource_id = None
        mock_log.error_message = "用户登录系统"
        mock_log.details = {}
        mock_log.ip_address = "*************"
        mock_log.user_agent = None
        mock_log.result = "success"
        mock_log.created_at = datetime.utcnow()
        
        mock_session.execute.return_value.scalar.return_value = 1
        mock_session.execute.return_value.scalars.return_value.all.return_value = [mock_log]
        
        mock_user = MagicMock()
        mock_user.username = "testuser"
        audit_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        query_result = await audit_service.query_audit_logs(
            tenant_id=tenant_id,
            page=1,
            page_size=20
        )
        
        assert query_result["total"] == 1
        assert len(query_result["logs"]) == 1
        assert query_result["logs"][0]["action"] == "LOGIN"
        
        # 3. 获取统计数据
        mock_redis_repo.get.return_value = None  # 缓存未命中
        
        audit_service._get_count_by_conditions = AsyncMock(side_effect=[100, 80, 15, 5])
        audit_service._get_top_actions = AsyncMock(return_value=[
            {"action": "LOGIN", "count": 50}
        ])
        audit_service._get_top_users = AsyncMock(return_value=[
            {"user_id": "user_123", "username": "testuser", "count": 25}
        ])
        audit_service._get_risk_distribution = AsyncMock(return_value={
            "low": 80, "medium": 15, "high": 5, "critical": 0
        })
        audit_service._get_time_series_data = AsyncMock(return_value=[
            {"time": "2025-01-15", "total": 50, "success": 45, "failed": 5, "high_risk": 2}
        ])
        
        stats_result = await audit_service.get_audit_statistics(
            tenant_id=tenant_id,
            group_by="day"
        )
        
        assert stats_result["total_logs"] == 100
        assert stats_result["success_logs"] == 80
        assert len(stats_result["top_actions"]) == 1
        
        # 4. 导出审计日志
        mock_session.execute.return_value.scalars.return_value.all.return_value = [mock_log]
        
        export_result = await audit_service.export_audit_logs(
            tenant_id=tenant_id,
            format="csv",
            include_details=True
        )
        
        assert "export_id" in export_result
        assert "download_url" in export_result
        assert export_result["record_count"] == 1
        
        # 验证Redis缓存操作
        mock_redis_repo.set.assert_called()

    @pytest.mark.asyncio
    async def test_audit_log_builder_integration(self, audit_service, mock_session):
        """测试AuditLogBuilder集成"""
        tenant_id = "tenant_123"
        
        # 模拟清除缓存和告警
        audit_service._clear_stats_cache = AsyncMock()
        audit_service._send_security_alert = AsyncMock()
        
        # 创建审计日志
        result = await audit_service.create_audit_log(
            tenant_id=tenant_id,
            user_id="user_123",
            action="DELETE_USER",
            resource_type="USER",
            resource_id="user_456",
            description="删除用户操作",
            details={"target_user": "user_456"},
            ip_address="*************",
            user_agent="Mozilla/5.0",
            status="success"
        )
        
        # 验证AuditLogBuilder被正确使用
        mock_session.add.assert_called_once()
        
        # 获取添加的审计日志对象
        added_log = mock_session.add.call_args[0][0]
        assert added_log.tenant_id == tenant_id
        assert added_log.user_id == "user_123"
        assert added_log.action == "DELETE_USER"
        assert added_log.resource_type == "USER"
        assert added_log.resource_id == "user_456"
        assert added_log.result == "success"

    @pytest.mark.asyncio
    async def test_risk_level_calculation_integration(self, audit_service):
        """测试风险级别计算集成"""
        # 测试不同类型的日志风险级别
        test_cases = [
            # (result, action, expected_risk)
            ("error", "LOGIN", "high"),
            ("failure", "LOGIN", "medium"),
            ("success", "DELETE", "medium"),
            ("success", "ADMIN_LOGIN", "medium"),
            ("success", "READ", "low"),
        ]
        
        for result, action, expected_risk in test_cases:
            mock_log = MagicMock()
            mock_log.result = result
            mock_log.action = action
            
            calculated_risk = audit_service._calculate_risk_level(mock_log)
            assert calculated_risk == expected_risk, f"Failed for {result}/{action}: expected {expected_risk}, got {calculated_risk}"

    @pytest.mark.asyncio
    async def test_session_context_handling(self, audit_service, mock_session):
        """测试会话上下文处理"""
        tenant_id = "tenant_123"
        
        audit_service._clear_stats_cache = AsyncMock()
        audit_service._send_security_alert = AsyncMock()
        
        # 创建包含会话信息的审计日志
        await audit_service.create_audit_log(
            tenant_id=tenant_id,
            user_id="user_123",
            action="LOGIN",
            resource_type="USER",
            ip_address="*************",
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
            status="success"
        )
        
        # 验证会话上下文被正确设置
        added_log = mock_session.add.call_args[0][0]
        
        # 检查会话上下文是否被设置
        if hasattr(added_log, 'session_context') and added_log.session_context:
            assert "ip_address" in added_log.session_context or added_log.ip_address == "*************"
            assert "user_agent" in added_log.session_context or added_log.user_agent == "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"

    @pytest.mark.asyncio
    async def test_cache_integration(self, audit_service, mock_redis_repo):
        """测试缓存集成"""
        tenant_id = "tenant_123"
        
        # 测试统计缓存
        cached_stats = {
            "total_logs": 100,
            "success_logs": 80,
            "failed_logs": 15,
            "high_risk_logs": 5
        }
        mock_redis_repo.get.return_value = cached_stats
        
        result = await audit_service.get_audit_statistics(
            tenant_id=tenant_id,
            group_by="day"
        )
        
        # 验证缓存命中
        assert result == cached_stats
        mock_redis_repo.get.assert_called_once()
        
        # 测试缓存清除
        mock_redis_repo.scan_keys.return_value = ["key1", "key2"]
        await audit_service._clear_stats_cache(tenant_id)
        
        mock_redis_repo.scan_keys.assert_called_once()
        mock_redis_repo.delete.assert_called_once_with("key1", "key2")

    @pytest.mark.asyncio
    async def test_export_file_generation(self, audit_service, mock_session):
        """测试导出文件生成"""
        tenant_id = "tenant_123"
        
        # 模拟审计日志数据
        mock_log = MagicMock()
        mock_log.id = 1
        mock_log.tenant_id = tenant_id
        mock_log.user_id = "user_123"
        mock_log.action = "LOGIN"
        mock_log.resource_type = "USER"
        mock_log.resource_id = None
        mock_log.error_message = "用户登录"
        mock_log.details = {"browser": "Chrome"}
        mock_log.ip_address = "*************"
        mock_log.user_agent = "Mozilla/5.0"
        mock_log.result = "success"
        mock_log.created_at = datetime.utcnow()
        
        mock_session.execute.return_value.scalars.return_value.all.return_value = [mock_log]
        
        # 模拟用户查询
        mock_user = MagicMock()
        mock_user.username = "testuser"
        audit_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        # 测试CSV导出
        csv_content, csv_filename, csv_size = await audit_service._generate_csv_file(
            [mock_log], True, "20250115_120000"
        )
        
        assert csv_filename == "audit_logs_20250115_120000.csv"
        assert csv_size > 0
        assert "日志ID" in csv_content
        assert "testuser" in csv_content
        
        # 测试JSON导出
        json_content, json_filename, json_size = await audit_service._generate_json_file(
            [mock_log], True, "20250115_120000"
        )
        
        assert json_filename == "audit_logs_20250115_120000.json"
        assert json_size > 0
        assert '"log_id": "1"' in json_content

    @pytest.mark.asyncio
    async def test_error_handling_integration(self, audit_service, mock_session):
        """测试错误处理集成"""
        # 测试数据库错误
        mock_session.commit.side_effect = Exception("Database error")
        mock_session.rollback = AsyncMock()
        
        with pytest.raises(BusinessError, match="创建审计日志失败"):
            await audit_service.create_audit_log(
                tenant_id="tenant_123",
                user_id="user_123",
                action="LOGIN",
                resource_type="USER",
                status="success"
            )
        
        # 验证回滚被调用
        mock_session.rollback.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
