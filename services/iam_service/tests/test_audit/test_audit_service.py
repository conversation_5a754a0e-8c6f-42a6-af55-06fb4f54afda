"""
审计服务测试

测试审计服务的各项功能
"""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from services.iam_service.services.audit_service import AuditService
from domain_common.models.iam_models import User, Tenant, AuditLog, AuditLogBuilder
from commonlib.exceptions.exceptions import ValidationError, BusinessError


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    session = AsyncMock()
    return session


@pytest.fixture
def mock_redis_repo():
    """模拟Redis仓库"""
    redis_repo = AsyncMock()
    return redis_repo


@pytest.fixture
def audit_service(mock_session, mock_redis_repo):
    """创建审计服务实例"""
    return AuditService(
        session=mock_session,
        redis_repo=mock_redis_repo,
        user_model=User,
        tenant_model=Tenant,
        audit_log_model=AuditLog
    )


class TestAuditService:
    """审计服务测试类"""

    @pytest.mark.asyncio
    async def test_create_audit_log_success(self, audit_service, mock_session):
        """测试成功创建审计日志"""
        # 准备测试数据
        tenant_id = "tenant_123"
        user_id = "user_123"
        action = "LOGIN"
        resource_type = "USER"
        
        # 模拟用户存在
        mock_user = MagicMock()
        mock_user.username = "testuser"
        audit_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        # 模拟清除缓存
        audit_service._clear_stats_cache = AsyncMock()
        
        # 执行测试
        result = await audit_service.create_audit_log(
            tenant_id=tenant_id,
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            description="用户登录系统",
            status="success"
        )
        
        # 验证结果
        assert "log_id" in result
        assert "created_at" in result
        
        # 验证数据库操作
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_audit_log_invalid_status(self, audit_service):
        """测试创建审计日志时使用无效状态"""
        with pytest.raises(ValidationError, match="无效的状态"):
            await audit_service.create_audit_log(
                tenant_id="tenant_123",
                user_id="user_123",
                action="LOGIN",
                resource_type="USER",
                status="invalid_status"
            )

    @pytest.mark.asyncio
    async def test_query_audit_logs_success(self, audit_service, mock_session):
        """测试成功查询审计日志"""
        # 准备测试数据
        tenant_id = "tenant_123"
        
        # 模拟数据库查询结果
        mock_log = MagicMock()
        mock_log.id = 1
        mock_log.tenant_id = tenant_id
        mock_log.user_id = "user_123"
        mock_log.action = "LOGIN"
        mock_log.resource_type = "USER"
        mock_log.resource_id = None
        mock_log.error_message = None
        mock_log.details = {}
        mock_log.ip_address = "*************"
        mock_log.user_agent = "Mozilla/5.0"
        mock_log.result = "success"
        mock_log.created_at = datetime.utcnow()
        
        # 模拟总数查询
        mock_session.execute.return_value.scalar.return_value = 1
        
        # 模拟分页查询
        mock_session.execute.return_value.scalars.return_value.all.return_value = [mock_log]
        
        # 模拟用户查询
        mock_user = MagicMock()
        mock_user.username = "testuser"
        audit_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        # 执行测试
        result = await audit_service.query_audit_logs(
            tenant_id=tenant_id,
            page=1,
            page_size=20
        )
        
        # 验证结果
        assert result["total"] == 1
        assert len(result["logs"]) == 1
        assert result["page"] == 1
        assert result["page_size"] == 20
        assert result["has_next"] is False
        
        # 验证日志数据
        log = result["logs"][0]
        assert log["log_id"] == "1"
        assert log["tenant_id"] == tenant_id
        assert log["action"] == "LOGIN"
        assert log["username"] == "testuser"

    @pytest.mark.asyncio
    async def test_get_audit_statistics_success(self, audit_service, mock_session, mock_redis_repo):
        """测试成功获取审计统计"""
        # 准备测试数据
        tenant_id = "tenant_123"
        
        # 模拟缓存未命中
        mock_redis_repo.get.return_value = None
        
        # 模拟统计查询
        audit_service._get_count_by_conditions = AsyncMock(side_effect=[100, 80, 15, 5])
        audit_service._get_top_actions = AsyncMock(return_value=[
            {"action": "LOGIN", "count": 50},
            {"action": "LOGOUT", "count": 30}
        ])
        audit_service._get_top_users = AsyncMock(return_value=[
            {"user_id": "user_1", "username": "user1", "count": 25},
            {"user_id": "user_2", "username": "user2", "count": 20}
        ])
        audit_service._get_risk_distribution = AsyncMock(return_value={
            "low": 80, "medium": 15, "high": 5, "critical": 0
        })
        audit_service._get_time_series_data = AsyncMock(return_value=[
            {"time": "2025-01-15", "total": 50, "success": 45, "failed": 5, "high_risk": 2}
        ])
        
        # 执行测试
        result = await audit_service.get_audit_statistics(
            tenant_id=tenant_id,
            group_by="day"
        )
        
        # 验证结果
        assert result["total_logs"] == 100
        assert result["success_logs"] == 80
        assert result["failed_logs"] == 15
        assert result["high_risk_logs"] == 5
        assert len(result["top_actions"]) == 2
        assert len(result["top_users"]) == 2
        assert "risk_distribution" in result
        assert "time_series" in result
        
        # 验证缓存操作
        mock_redis_repo.set.assert_called_once()

    @pytest.mark.asyncio
    async def test_export_audit_logs_csv(self, audit_service, mock_session, mock_redis_repo):
        """测试导出审计日志为CSV格式"""
        # 准备测试数据
        tenant_id = "tenant_123"
        
        # 模拟数据库查询结果
        mock_log = MagicMock()
        mock_log.id = 1
        mock_log.tenant_id = tenant_id
        mock_log.user_id = "user_123"
        mock_log.action = "LOGIN"
        mock_log.resource_type = "USER"
        mock_log.resource_id = None
        mock_log.error_message = "用户登录"
        mock_log.details = {"browser": "Chrome"}
        mock_log.ip_address = "*************"
        mock_log.user_agent = "Mozilla/5.0"
        mock_log.result = "success"
        mock_log.created_at = datetime.utcnow()
        
        mock_session.execute.return_value.scalars.return_value.all.return_value = [mock_log]
        
        # 模拟用户查询
        mock_user = MagicMock()
        mock_user.username = "testuser"
        audit_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        # 执行测试
        result = await audit_service.export_audit_logs(
            tenant_id=tenant_id,
            format="csv",
            include_details=True
        )
        
        # 验证结果
        assert "export_id" in result
        assert "download_url" in result
        assert "file_name" in result
        assert result["file_name"].endswith(".csv")
        assert result["record_count"] == 1
        assert "created_at" in result
        assert "expires_at" in result
        
        # 验证Redis缓存
        mock_redis_repo.set.assert_called_once()

    @pytest.mark.asyncio
    async def test_risk_level_calculation(self, audit_service):
        """测试风险级别计算"""
        # 测试错误操作
        mock_log_error = MagicMock()
        mock_log_error.result = "error"
        mock_log_error.action = "LOGIN"
        assert audit_service._calculate_risk_level(mock_log_error) == "high"
        
        # 测试失败操作
        mock_log_failure = MagicMock()
        mock_log_failure.result = "failure"
        mock_log_failure.action = "LOGIN"
        assert audit_service._calculate_risk_level(mock_log_failure) == "medium"
        
        # 测试高风险操作
        mock_log_delete = MagicMock()
        mock_log_delete.result = "success"
        mock_log_delete.action = "DELETE"
        assert audit_service._calculate_risk_level(mock_log_delete) == "medium"
        
        # 测试普通操作
        mock_log_normal = MagicMock()
        mock_log_normal.result = "success"
        mock_log_normal.action = "READ"
        assert audit_service._calculate_risk_level(mock_log_normal) == "low"

    @pytest.mark.asyncio
    async def test_security_alert_for_high_risk(self, audit_service, mock_session):
        """测试高风险操作的安全告警"""
        # 准备测试数据
        tenant_id = "tenant_123"
        
        # 模拟安全告警方法
        audit_service._send_security_alert = AsyncMock()
        audit_service._clear_stats_cache = AsyncMock()
        
        # 执行测试 - 创建错误日志
        await audit_service.create_audit_log(
            tenant_id=tenant_id,
            user_id="user_123",
            action="LOGIN",
            resource_type="USER",
            status="error",
            description="登录失败"
        )
        
        # 验证安全告警被调用
        audit_service._send_security_alert.assert_called_once()

    @pytest.mark.asyncio
    async def test_cache_operations(self, audit_service, mock_redis_repo):
        """测试缓存操作"""
        tenant_id = "tenant_123"
        
        # 测试清除统计缓存
        mock_redis_repo.scan_keys.return_value = ["key1", "key2"]
        await audit_service._clear_stats_cache(tenant_id)
        
        # 验证缓存操作
        mock_redis_repo.scan_keys.assert_called_once()
        mock_redis_repo.delete.assert_called_once_with("key1", "key2")


if __name__ == "__main__":
    pytest.main([__file__])
