"""
租户服务测试
"""

import pytest
from services.tenant_service import TenantService


@pytest.mark.asyncio
async def test_create_tenant(mock_redis_repo, sample_tenant_data):
    """测试创建租户"""
    service = TenantService(redis_repo=mock_redis_repo)
    
    result = await service.create_tenant(sample_tenant_data)
    
    assert "tenant_id" in result
    assert result["tenant_name"] == sample_tenant_data["tenant_name"]
    assert result["tenant_code"] == sample_tenant_data["tenant_code"]
    assert result["status"] == "pending"
    
    # 验证缓存调用
    mock_redis_repo.set.assert_called_once()


@pytest.mark.asyncio
async def test_list_tenants(mock_redis_repo):
    """测试获取租户列表"""
    service = TenantService(redis_repo=mock_redis_repo)
    
    result = await service.list_tenants({})
    
    assert "items" in result
    assert "has_more" in result
    assert "total" in result
    assert isinstance(result["items"], list)


@pytest.mark.asyncio
async def test_get_tenant_detail(mock_redis_repo):
    """测试获取租户详情"""
    service = TenantService(redis_repo=mock_redis_repo)
    
    # 模拟缓存未命中
    mock_redis_repo.get.return_value = None
    
    result = await service.get_tenant_detail({"tenant_id": "test_tenant"})
    
    assert "tenant_id" in result
    assert "tenant_name" in result
    assert "status" in result
    assert "statistics" in result
    
    # 验证缓存调用
    mock_redis_repo.get.assert_called_once()
    mock_redis_repo.set.assert_called_once()


@pytest.mark.asyncio
async def test_update_tenant(mock_redis_repo, sample_tenant_data):
    """测试更新租户"""
    service = TenantService(redis_repo=mock_redis_repo)
    
    update_data = {
        "tenant_id": "test_tenant",
        **sample_tenant_data
    }
    
    result = await service.update_tenant(update_data)
    
    assert result["tenant_id"] == "test_tenant"
    assert "updated_at" in result
    
    # 验证缓存清理
    mock_redis_repo.delete.assert_called_once()


@pytest.mark.asyncio
async def test_delete_tenant(mock_redis_repo):
    """测试删除租户"""
    service = TenantService(redis_repo=mock_redis_repo)
    
    delete_data = {
        "tenant_id": "test_tenant",
        "delete_type": "soft"
    }
    
    result = await service.delete_tenant(delete_data)
    
    assert result["tenant_id"] == "test_tenant"
    assert result["delete_type"] == "soft"
    assert "deleted_at" in result
    assert "cleanup_summary" in result
    
    # 验证缓存清理
    mock_redis_repo.delete.assert_called_once()
