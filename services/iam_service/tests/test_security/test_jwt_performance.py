"""
JWT管理器性能测试

测试JWT管理器在高负载下的性能表现
"""

import pytest
import asyncio
import time
from datetime import datetime
from unittest.mock import AsyncMock

from security.jwt_manager import JWTManager


@pytest.mark.performance
class TestJWTManagerPerformance:
    """JWT管理器性能测试类"""
    
    @pytest.fixture
    async def jwt_manager_perf(self):
        """性能测试用的JWT管理器"""
        mock_redis = AsyncMock()
        mock_redis.set.return_value = None
        mock_redis.get.return_value = {"session_id": "test_session"}
        mock_redis.keys.return_value = []
        mock_redis.delete.return_value = None
        mock_redis.lpush.return_value = None
        mock_redis.ltrim.return_value = None
        
        return JWTManager(
            redis_repo=mock_redis,
            access_token_expire_minutes=30,
            refresh_token_expire_days=7
        )
    
    @pytest.fixture
    def sample_user_data(self):
        """示例用户数据"""
        return {
            "user_id": "perf_user_123",
            "tenant_id": "perf_tenant_456",
            "session_id": "perf_session_789",
            "roles": ["admin", "user"],
            "permissions": ["user:read", "user:write", "role:read"],
            "device_fingerprint": "perf_device_abc123",
            "ip_address": "*************"
        }
    
    @pytest.mark.asyncio
    async def test_token_generation_performance(self, jwt_manager_perf, sample_user_data):
        """测试令牌生成性能"""
        iterations = 1000
        
        start_time = time.time()
        
        tasks = []
        for i in range(iterations):
            task = jwt_manager_perf.generate_token_pair(
                user_id=f"{sample_user_data['user_id']}_{i}",
                tenant_id=sample_user_data["tenant_id"],
                session_id=f"{sample_user_data['session_id']}_{i}",
                roles=sample_user_data["roles"],
                permissions=sample_user_data["permissions"],
                device_fingerprint=sample_user_data["device_fingerprint"],
                ip_address=sample_user_data["ip_address"]
            )
            tasks.append(task)
        
        # 并发执行所有任务
        token_pairs = await asyncio.gather(*tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 验证结果
        assert len(token_pairs) == iterations
        for token_pair in token_pairs:
            assert token_pair.access_token is not None
            assert token_pair.refresh_token is not None
        
        # 性能指标
        tokens_per_second = iterations / duration
        avg_time_per_token = duration / iterations * 1000  # 毫秒
        
        print(f"\n令牌生成性能测试结果:")
        print(f"总时间: {duration:.2f}秒")
        print(f"每秒生成令牌数: {tokens_per_second:.2f}")
        print(f"平均每个令牌生成时间: {avg_time_per_token:.2f}毫秒")
        
        # 性能断言（根据实际环境调整）
        assert tokens_per_second > 100  # 每秒至少100个令牌
        assert avg_time_per_token < 50   # 平均每个令牌生成时间小于50毫秒
    
    @pytest.mark.asyncio
    async def test_token_verification_performance(self, jwt_manager_perf, sample_user_data):
        """测试令牌验证性能"""
        # 先生成一些令牌
        token_pairs = []
        for i in range(100):
            token_pair = await jwt_manager_perf.generate_token_pair(
                user_id=f"{sample_user_data['user_id']}_{i}",
                tenant_id=sample_user_data["tenant_id"],
                session_id=f"{sample_user_data['session_id']}_{i}",
                roles=sample_user_data["roles"],
                permissions=sample_user_data["permissions"],
                device_fingerprint=sample_user_data["device_fingerprint"],
                ip_address=sample_user_data["ip_address"]
            )
            token_pairs.append(token_pair)
        
        # 测试验证性能
        iterations = 1000
        start_time = time.time()
        
        tasks = []
        for i in range(iterations):
            # 随机选择一个令牌进行验证
            token_pair = token_pairs[i % len(token_pairs)]
            task = jwt_manager_perf.verify_access_token(token_pair.access_token)
            tasks.append(task)
        
        # 并发执行所有验证任务
        payloads = await asyncio.gather(*tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 验证结果
        assert len(payloads) == iterations
        valid_payloads = [p for p in payloads if p is not None]
        assert len(valid_payloads) == iterations
        
        # 性能指标
        verifications_per_second = iterations / duration
        avg_time_per_verification = duration / iterations * 1000  # 毫秒
        
        print(f"\n令牌验证性能测试结果:")
        print(f"总时间: {duration:.2f}秒")
        print(f"每秒验证令牌数: {verifications_per_second:.2f}")
        print(f"平均每个令牌验证时间: {avg_time_per_verification:.2f}毫秒")
        
        # 性能断言
        assert verifications_per_second > 500  # 每秒至少500次验证
        assert avg_time_per_verification < 10   # 平均每次验证时间小于10毫秒
    
    @pytest.mark.asyncio
    async def test_concurrent_mixed_operations(self, jwt_manager_perf, sample_user_data):
        """测试混合操作的并发性能"""
        iterations = 500
        
        start_time = time.time()
        
        # 先生成一些令牌用于验证和刷新
        initial_tokens = []
        for i in range(50):
            token_pair = await jwt_manager_perf.generate_token_pair(
                user_id=f"{sample_user_data['user_id']}_{i}",
                tenant_id=sample_user_data["tenant_id"],
                session_id=f"{sample_user_data['session_id']}_{i}",
                roles=sample_user_data["roles"],
                permissions=sample_user_data["permissions"],
                device_fingerprint=sample_user_data["device_fingerprint"],
                ip_address=sample_user_data["ip_address"]
            )
            initial_tokens.append(token_pair)
        
        # 混合操作任务
        tasks = []
        
        for i in range(iterations):
            operation_type = i % 3
            
            if operation_type == 0:
                # 生成新令牌
                task = jwt_manager_perf.generate_token_pair(
                    user_id=f"mixed_{sample_user_data['user_id']}_{i}",
                    tenant_id=sample_user_data["tenant_id"],
                    session_id=f"mixed_{sample_user_data['session_id']}_{i}",
                    roles=sample_user_data["roles"],
                    permissions=sample_user_data["permissions"],
                    device_fingerprint=sample_user_data["device_fingerprint"],
                    ip_address=sample_user_data["ip_address"]
                )
            elif operation_type == 1:
                # 验证令牌
                token_pair = initial_tokens[i % len(initial_tokens)]
                task = jwt_manager_perf.verify_access_token(token_pair.access_token)
            else:
                # 撤销令牌
                token_pair = initial_tokens[i % len(initial_tokens)]
                task = jwt_manager_perf.revoke_token(token_pair.access_token, "access")
            
            tasks.append(task)
        
        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 统计结果
        successful_operations = len([r for r in results if not isinstance(r, Exception)])
        operations_per_second = iterations / duration
        avg_time_per_operation = duration / iterations * 1000  # 毫秒
        
        print(f"\n混合操作并发性能测试结果:")
        print(f"总操作数: {iterations}")
        print(f"成功操作数: {successful_operations}")
        print(f"总时间: {duration:.2f}秒")
        print(f"每秒操作数: {operations_per_second:.2f}")
        print(f"平均每个操作时间: {avg_time_per_operation:.2f}毫秒")
        
        # 性能断言
        assert successful_operations >= iterations * 0.95  # 至少95%的操作成功
        assert operations_per_second > 200  # 每秒至少200个操作
        assert avg_time_per_operation < 25   # 平均每个操作时间小于25毫秒
    
    @pytest.mark.asyncio
    async def test_memory_usage_stability(self, jwt_manager_perf, sample_user_data):
        """测试内存使用稳定性"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 大量令牌操作
        iterations = 2000
        
        for batch in range(10):  # 分批执行
            tasks = []
            for i in range(iterations // 10):
                task = jwt_manager_perf.generate_token_pair(
                    user_id=f"memory_test_{batch}_{i}",
                    tenant_id=sample_user_data["tenant_id"],
                    session_id=f"memory_session_{batch}_{i}",
                    roles=sample_user_data["roles"],
                    permissions=sample_user_data["permissions"],
                    device_fingerprint=sample_user_data["device_fingerprint"],
                    ip_address=sample_user_data["ip_address"]
                )
                tasks.append(task)
            
            # 执行批次
            await asyncio.gather(*tasks)
            
            # 检查内存使用
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = current_memory - initial_memory
            
            print(f"批次 {batch + 1}: 内存使用 {current_memory:.2f}MB (+{memory_increase:.2f}MB)")
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_memory_increase = final_memory - initial_memory
        
        print(f"\n内存稳定性测试结果:")
        print(f"初始内存: {initial_memory:.2f}MB")
        print(f"最终内存: {final_memory:.2f}MB")
        print(f"总内存增长: {total_memory_increase:.2f}MB")
        
        # 内存使用断言（根据实际情况调整）
        assert total_memory_increase < 100  # 总内存增长小于100MB
    
    @pytest.mark.asyncio
    async def test_permissions_hash_performance(self, jwt_manager_perf):
        """测试权限哈希计算性能"""
        # 不同大小的权限列表
        permission_sets = [
            ["user:read"],  # 1个权限
            ["user:read", "user:write", "role:read"],  # 3个权限
            [f"resource_{i}:action_{j}" for i in range(10) for j in range(5)],  # 50个权限
            [f"resource_{i}:action_{j}" for i in range(50) for j in range(10)],  # 500个权限
        ]
        
        for i, permissions in enumerate(permission_sets):
            iterations = 10000
            start_time = time.time()
            
            # 计算权限哈希
            for _ in range(iterations):
                jwt_manager_perf._calculate_permissions_hash(permissions)
            
            end_time = time.time()
            duration = end_time - start_time
            
            hashes_per_second = iterations / duration
            avg_time_per_hash = duration / iterations * 1000000  # 微秒
            
            print(f"\n权限哈希性能测试 - {len(permissions)}个权限:")
            print(f"每秒哈希计算数: {hashes_per_second:.2f}")
            print(f"平均每次哈希时间: {avg_time_per_hash:.2f}微秒")
            
            # 性能断言
            assert hashes_per_second > 1000  # 每秒至少1000次哈希计算
            assert avg_time_per_hash < 1000   # 平均每次哈希时间小于1000微秒
    
    @pytest.mark.asyncio
    async def test_token_size_analysis(self, jwt_manager_perf, sample_user_data):
        """测试令牌大小分析"""
        # 不同权限数量的令牌大小
        permission_counts = [1, 10, 50, 100, 500]
        
        for count in permission_counts:
            permissions = [f"resource_{i}:action" for i in range(count)]
            
            token_pair = await jwt_manager_perf.generate_token_pair(
                user_id=sample_user_data["user_id"],
                tenant_id=sample_user_data["tenant_id"],
                session_id=sample_user_data["session_id"],
                roles=sample_user_data["roles"],
                permissions=permissions,
                device_fingerprint=sample_user_data["device_fingerprint"],
                ip_address=sample_user_data["ip_address"]
            )
            
            access_token_size = len(token_pair.access_token.encode('utf-8'))
            refresh_token_size = len(token_pair.refresh_token.encode('utf-8'))
            
            print(f"\n令牌大小分析 - {count}个权限:")
            print(f"访问令牌大小: {access_token_size} 字节")
            print(f"刷新令牌大小: {refresh_token_size} 字节")
            
            # 大小断言（根据实际需求调整）
            assert access_token_size < 8192   # 访问令牌小于8KB
            assert refresh_token_size < 1024  # 刷新令牌小于1KB
