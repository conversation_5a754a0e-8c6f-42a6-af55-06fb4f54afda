"""
JWT管理器集成测试

测试JWT管理器与Redis的实际集成
"""

import pytest
import asyncio
from datetime import datetime, timedelta

from commonlib.storages.persistence.redis.client import RedisConnector
from security.jwt_manager import JWTManager, TokenPair, TokenPayload
from commonlib.storages.persistence.redis.repository import RedisRepository


@pytest.mark.integration
class TestJWTManagerIntegration:
    """JWT管理器集成测试类"""

    @pytest.fixture
    async def real_redis_repo(self):
        """真实的Redis仓库（需要Redis服务器运行）"""
        # 注意：这需要真实的Redis连接
        # 在CI/CD环境中，应该使用测试Redis实例
        try:
            from redis import Redis
            redis_conn = RedisConnector()
            redis_conn.load_config({
                "REDIS_HOST": "**************",
                "REDIS_PORT": 6378,
                "REDIS_DB": 0,
                "REDIS_USERNAME": "",
                "REDIS_PASSWORD": "thismore@123456",
                "REDIS_SSL": False,
                "REDIS_POOL_SIZE": 10,
                "REDIS_MAX_CONNECTIONS": 10,
                "REDIS_POOL_TIMEOUT": 5,
                "REDIS_POOL_RECYCLE": 3600,
                "REDIS_RETRY_ON_TIMEOUT": True,
                "REDIS_POOL_PRE_PING": True,
                "REDIS_DECODE_RESPONSE": True
            })

            # 创建Redis仓库实例
            repo = RedisRepository(key_prefix="test_jwt", redis_connector=redis_conn)

            # 清理测试数据
            await self._cleanup_test_data(repo)

            yield repo

            # 测试后清理
            await self._cleanup_test_data(repo)

        except Exception as e:
            pytest.skip(f"Redis not available: {e}")

    async def _cleanup_test_data(self, redis_repo: RedisRepository):
        """清理测试数据"""
        try:
            # 删除所有测试相关的键
            patterns = [
                "test_jwt:refresh_token:*",
                "test_jwt:blacklist_token:*",
                "test_jwt:audit_log:*",
                "test_jwt:token_errors"
            ]

            for pattern in patterns:
                keys = await redis_repo.keys(pattern)
                if keys:
                    for key in keys:
                        await redis_repo.delete(key)
        except Exception:
            pass

    @pytest.fixture
    async def jwt_manager_real(self, real_redis_repo: RedisRepository):
        """使用真实Redis的JWT管理器"""
        return JWTManager(
            redis_repo=real_redis_repo,
            access_token_expire_minutes=1,  # 短期过期用于测试
            refresh_token_expire_days=1
        )

    @pytest.fixture
    def sample_user_data(self):
        """示例用户数据"""
        return {
            "user_id": "integration_user_123",
            "tenant_id": "integration_tenant_456",
            "session_id": "integration_session_789",
            "roles": ["admin", "user"],
            "permissions": ["user:read", "user:write", "role:read"],
            "device_fingerprint": "integration_device_abc123",
            "ip_address": "*************"
        }

    @pytest.mark.asyncio
    async def test_full_token_lifecycle(self, jwt_manager_real, sample_user_data):
        """测试完整的令牌生命周期"""
        # 1. 生成令牌对
        token_pair = await jwt_manager_real.generate_token_pair(
            user_id=sample_user_data["user_id"],
            tenant_id=sample_user_data["tenant_id"],
            session_id=sample_user_data["session_id"],
            roles=sample_user_data["roles"],
            permissions=sample_user_data["permissions"],
            device_fingerprint=sample_user_data["device_fingerprint"],
            ip_address=sample_user_data["ip_address"]
        )

        assert isinstance(token_pair, TokenPair)
        assert token_pair.access_token is not None
        assert token_pair.refresh_token is not None

        # 2. 验证访问令牌
        token_payload = await jwt_manager_real.verify_access_token(token_pair.access_token)

        assert isinstance(token_payload, TokenPayload)
        assert token_payload.user_id == sample_user_data["user_id"]
        assert token_payload.tenant_id == sample_user_data["tenant_id"]

        # 3. 刷新令牌对
        new_token_pair = await jwt_manager_real.refresh_token_pair(
            token_pair.refresh_token,
            device_fingerprint=sample_user_data["device_fingerprint"],
            ip_address=sample_user_data["ip_address"]
        )

        assert isinstance(new_token_pair, TokenPair)
        assert new_token_pair.access_token != token_pair.access_token
        assert new_token_pair.refresh_token != token_pair.refresh_token

        # 4. 验证新的访问令牌
        new_token_payload = await jwt_manager_real.verify_access_token(new_token_pair.access_token)

        assert isinstance(new_token_payload, TokenPayload)
        assert new_token_payload.user_id == sample_user_data["user_id"]

        # 5. 撤销新的访问令牌
        revoke_result = await jwt_manager_real.revoke_token(new_token_pair.access_token, "access")
        assert revoke_result is True

        # 6. 验证被撤销的令牌
        revoked_payload = await jwt_manager_real.verify_access_token(new_token_pair.access_token)
        assert revoked_payload is None

    @pytest.mark.asyncio
    async def test_concurrent_token_operations(self, jwt_manager_real, sample_user_data):
        """测试并发令牌操作"""
        # 并发生成多个令牌对
        tasks = []
        for i in range(5):
            task = jwt_manager_real.generate_token_pair(
                user_id=f"{sample_user_data['user_id']}_{i}",
                tenant_id=sample_user_data["tenant_id"],
                session_id=f"{sample_user_data['session_id']}_{i}",
                roles=sample_user_data["roles"],
                permissions=sample_user_data["permissions"],
                device_fingerprint=sample_user_data["device_fingerprint"],
                ip_address=sample_user_data["ip_address"]
            )
            tasks.append(task)

        # 等待所有任务完成
        token_pairs = await asyncio.gather(*tasks)

        # 验证所有令牌对都成功生成
        assert len(token_pairs) == 5
        for token_pair in token_pairs:
            assert isinstance(token_pair, TokenPair)
            assert token_pair.access_token is not None
            assert token_pair.refresh_token is not None

        # 验证所有访问令牌都有效
        verify_tasks = []
        for token_pair in token_pairs:
            task = jwt_manager_real.verify_access_token(token_pair.access_token)
            verify_tasks.append(task)

        payloads = await asyncio.gather(*verify_tasks)

        # 验证所有载荷都有效
        assert len(payloads) == 5
        for payload in payloads:
            assert isinstance(payload, TokenPayload)

    @pytest.mark.asyncio
    async def test_token_expiration(self, real_redis_repo, sample_user_data):
        """测试令牌过期"""
        # 创建一个访问令牌过期时间很短的JWT管理器
        jwt_manager = JWTManager(
            redis_repo=real_redis_repo,
            access_token_expire_minutes=0.01,  # 0.6秒过期
            refresh_token_expire_days=1
        )

        # 生成令牌对
        token_pair = await jwt_manager.generate_token_pair(
            user_id=sample_user_data["user_id"],
            tenant_id=sample_user_data["tenant_id"],
            session_id=sample_user_data["session_id"],
            roles=sample_user_data["roles"],
            permissions=sample_user_data["permissions"]
        )

        # 立即验证应该成功
        payload = await jwt_manager.verify_access_token(token_pair.access_token)
        assert payload is not None

        # 等待令牌过期
        await asyncio.sleep(1)

        # 验证过期令牌应该失败
        expired_payload = await jwt_manager.verify_access_token(token_pair.access_token)
        assert expired_payload is None

    @pytest.mark.asyncio
    async def test_refresh_token_one_time_use(self, jwt_manager_real, sample_user_data):
        """测试刷新令牌一次性使用"""
        # 生成令牌对
        token_pair = await jwt_manager_real.generate_token_pair(
            user_id=sample_user_data["user_id"],
            tenant_id=sample_user_data["tenant_id"],
            session_id=sample_user_data["session_id"],
            roles=sample_user_data["roles"],
            permissions=sample_user_data["permissions"],
            device_fingerprint=sample_user_data["device_fingerprint"],
            ip_address=sample_user_data["ip_address"]
        )

        # 第一次刷新应该成功
        new_token_pair = await jwt_manager_real.refresh_token_pair(
            token_pair.refresh_token,
            device_fingerprint=sample_user_data["device_fingerprint"],
            ip_address=sample_user_data["ip_address"]
        )
        assert new_token_pair is not None

        # 第二次使用相同的刷新令牌应该失败
        second_refresh = await jwt_manager_real.refresh_token_pair(
            token_pair.refresh_token,
            device_fingerprint=sample_user_data["device_fingerprint"],
            ip_address=sample_user_data["ip_address"]
        )
        assert second_refresh is None

    @pytest.mark.asyncio
    async def test_device_fingerprint_security(self, jwt_manager_real, sample_user_data):
        """测试设备指纹安全性"""
        # 生成令牌对
        token_pair = await jwt_manager_real.generate_token_pair(
            user_id=sample_user_data["user_id"],
            tenant_id=sample_user_data["tenant_id"],
            session_id=sample_user_data["session_id"],
            roles=sample_user_data["roles"],
            permissions=sample_user_data["permissions"],
            device_fingerprint=sample_user_data["device_fingerprint"],
            ip_address=sample_user_data["ip_address"]
        )

        # 使用不同的设备指纹刷新应该失败
        failed_refresh = await jwt_manager_real.refresh_token_pair(
            token_pair.refresh_token,
            device_fingerprint="different_device_fingerprint",
            ip_address=sample_user_data["ip_address"]
        )
        assert failed_refresh is None

        # 使用正确的设备指纹刷新应该成功
        success_refresh = await jwt_manager_real.refresh_token_pair(
            token_pair.refresh_token,
            device_fingerprint=sample_user_data["device_fingerprint"],
            ip_address=sample_user_data["ip_address"]
        )
        assert success_refresh is not None

    @pytest.mark.asyncio
    async def test_token_statistics(self, jwt_manager_real, sample_user_data):
        """测试令牌统计功能"""
        # 初始统计
        initial_stats = await jwt_manager_real.get_token_stats()
        initial_refresh_count = initial_stats["active_refresh_tokens"]
        initial_blacklist_count = initial_stats["blacklisted_tokens"]

        # 生成一些令牌
        token_pairs = []
        for i in range(3):
            token_pair = await jwt_manager_real.generate_token_pair(
                user_id=f"{sample_user_data['user_id']}_{i}",
                tenant_id=sample_user_data["tenant_id"],
                session_id=f"{sample_user_data['session_id']}_{i}",
                roles=sample_user_data["roles"],
                permissions=sample_user_data["permissions"]
            )
            token_pairs.append(token_pair)

        # 检查统计增加
        after_generation_stats = await jwt_manager_real.get_token_stats()
        assert after_generation_stats["active_refresh_tokens"] == initial_refresh_count + 3

        # 撤销一个访问令牌
        await jwt_manager_real.revoke_token(token_pairs[0].access_token, "access")

        # 检查黑名单统计增加
        after_revoke_stats = await jwt_manager_real.get_token_stats()
        assert after_revoke_stats["blacklisted_tokens"] == initial_blacklist_count + 1

    @pytest.mark.asyncio
    async def test_cleanup_expired_tokens(self, real_redis_repo, sample_user_data):
        """测试清理过期令牌"""
        # 创建一个刷新令牌过期时间很短的JWT管理器
        jwt_manager = JWTManager(
            redis_repo=real_redis_repo,
            access_token_expire_minutes=30,
            refresh_token_expire_days=0.001  # 约1.44分钟过期
        )

        # 生成一些令牌
        for i in range(3):
            await jwt_manager.generate_token_pair(
                user_id=f"{sample_user_data['user_id']}_{i}",
                tenant_id=sample_user_data["tenant_id"],
                session_id=f"{sample_user_data['session_id']}_{i}",
                roles=sample_user_data["roles"],
                permissions=sample_user_data["permissions"]
            )

        # 等待令牌过期
        await asyncio.sleep(90)  # 等待1.5分钟

        # 清理过期令牌
        cleanup_result = await jwt_manager.cleanup_expired_tokens()

        # 验证清理结果
        assert cleanup_result["cleaned_refresh_tokens"] >= 0
        assert "remaining_blacklist_tokens" in cleanup_result
