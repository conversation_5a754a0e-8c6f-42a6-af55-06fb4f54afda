"""
JWT密钥管理测试

测试JWT管理器的密钥缓存和分布式锁功能
"""

import pytest
import asyncio
import os
from unittest.mock import AsyncMock, patch

from security.jwt_manager import JWTManager
from commonlib.storages.persistence.redis.repository import RedisRepository


class TestJWTKeyManagement:
    """JWT密钥管理测试类"""

    @pytest.fixture
    async def redis_repo(self):
        """模拟Redis仓库"""
        mock_repo = AsyncMock(spec=RedisRepository)

        # 设置默认返回值
        mock_repo.set.return_value = True
        mock_repo.get.return_value = None
        mock_repo.delete.return_value = True
        mock_repo.exists.return_value = False
        mock_repo.lpush.return_value = None
        mock_repo.ltrim.return_value = None
        mock_repo.eval.return_value = 1

        return mock_repo

    @pytest.fixture
    def sample_key_pair(self):
        """示例密钥对"""
        private_key = """-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
xhXBaYWJlAUdamDdGcQhF/wANdCGga+vTz9Wmu0wZWKOv2iSdHPeJJl+n4MJurxI
TsTZJmuCh4NqDjgOWjSHb9ku8Bbh2lUr+Og87MtzdRVxHOaHWiAoqLLsrTtH4+Jq
5TjyrgjSUInP2TbzAn4Mc9HdFTFEjwhlSzHK69Y5eF7HQBoJfXw8FPuv/xGxu4Jr
Zts8+7mWvSNjSiJGMl7OkEd/uOxHTbpUcAT7dmKWHuruRuUcen43RX+8/lXR2TKa
GKd0VIQxqZXSwcKBrfh13An2gOLDYdAshT7132aie5Q6jIFBPVevLtfmdLWhmUWf
p1riiyiNAgMBAAECggEAAmOSpps1VAmJePXhEr6OfwIDAQABAoIBAQCTmWpBZ7J/
LwFdpFnFE46w1zP0nTbzjHBZ3SlOCWLSI/PqJwrxHPa5hsVDlM4jckXNH4ZBg7na
2/b6hqJGGWpCDgLiaPKL6Fc91dDvN4f6k+vCz4dDkbdXRp4AVBubHObLsxrkKyuN
7nb6Heoi4eXMNAvzNVoGXuaschkx0jmDCyVlgxnxI5h3DiuDUyR0QSBqVHtfnhNl
3YOBAKJMCVBK+7ErKNW3ha2l/pzvHQyISHyNDiuVx7cU0aLKe2pWADVMxiiiPMvA
FrfGlMeAZmisNGqbUBxAaydjySQVHaPz/UN9FuQocpycivcqCXFF8QiJ0QOh02P9
Tz04sNRtmw0BAoGBAOGIrANRPrfCAFE3hUB3yrOZHqhXpfiFBjjYQ1SdVyxQoSqf
9Z2nTruun4fFyPANkQe0iCy0DDL9ckI3+OoOiHGLDxFXyIFBprhDyLXkdM6ykSPi
VBzPxD0A+uHmDxGYEW6En8pu8uj6XjBQoNEoNHmqDmUDwXMb2tR9FN0cH8MBAoGB
AMpFZvGq0Pj+hnyf2LdMrL1SmcGZA+7s5QjpfTU5Q+VoYAjyQ3dynHdvFiapDdKz
aBtjD_L6VtVbfHtloQeXdpw9OC2HdDrpulrKjZcFBgBtKhMtE5D6f+MlPaXlhOxI
kAGRHRi2Zrb8WjGllJn4fn+VmaiLrrWoVBcHkPn2VQJ5AoGBAKhXdFuLqAqXs1WS
AfNyKRnfVYdE+P7I5f1roF1A9mT+2d0E4o+cQ+cwfBFaE7+adMp9V9UyC0f6tdgR
3CqQrXH8vIjfn4kVBuX40SWH+n0N+b8dknZxvz0UTEs0vTh7BboBBHyMeVd2+6LZ
aMKLAoGBAJ8UBydJ3yVMgi91xvBjkpAOEEe8QmAfLmCMpGFUlDAHBgDcTAhW2Cqw
f9T3c8rSMpGVeYs4YjLcpEpQaBNbQB+XSmtdV5TIupOsMqz0s4Vkdkfn+f8EibDl
bWfenWVA0HMpILfgpEggM3Qe1rX+JlsgHVqcXgPvAoGBAKTmRAQjfnfzQcQBuDLL
OHi3iJvAb5KB8qZ5ZNjHDSVudjgdBtfsiHQqLOD4I0NKtWu5dUbqkhF0bnAcVSrv
AT6N4t1hVMoGTrMoiGtJxGrk+CY6tVZoaqHdvxXsEa7+5dIgHLuOlYvee4C5
-----END PRIVATE KEY-----"""

        public_key = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu1SU1L7VLPHCgcYVwWmF
iZQFHWpg3RnEIRf8ADXQhoGvr08/VprtMGVijr9oknRz3iSZfp+DCbq8SE7E2SZr
goeDag44Dlo0h2/ZLvAW4dpVK/joPOzLc3UVcRzmh1ogKKiy7K07R+PiauU48q4I
0lCJz9k28wJ+DHPR3RUxRI8IZUsxyuvWOXhex0AaCX18PBT7r/8RsbuCa2bbPPu5
lr0jY0oiRjJezpBHf7jsR026VHAE+3Zilh7q7kblHHp+N0V/vP5V0dkymhindFSE
MamV0sHCga34ddwJ9oDiw2HQLIUe9d9montUOoyBQT1Xry7X5nS1oZlFn6da4oso
jQIDAQAB
-----END PUBLIC KEY-----"""

        return private_key, public_key

    @pytest.mark.asyncio
    async def test_init_keys_with_provided_keys(self, redis_repo, sample_key_pair):
        """测试使用提供的密钥初始化"""
        private_key, public_key = sample_key_pair

        jwt_manager = JWTManager(
            redis_repo=redis_repo,
            private_key=private_key,
            public_key=public_key
        )

        await jwt_manager._ensure_keys_initialized()

        assert jwt_manager.private_key == private_key
        assert jwt_manager.public_key == public_key
        assert jwt_manager._keys_initialized is True

    @pytest.mark.asyncio
    async def test_init_keys_from_environment(self, redis_repo, sample_key_pair):
        """测试从环境变量初始化密钥"""
        private_key, public_key = sample_key_pair

        with patch.dict(os.environ, {
            'JWT_PRIVATE_KEY': private_key,
            'JWT_PUBLIC_KEY': public_key
        }):
            jwt_manager = JWTManager(redis_repo=redis_repo)
            await jwt_manager._ensure_keys_initialized()

            assert jwt_manager.private_key == private_key
            assert jwt_manager.public_key == public_key

    @pytest.mark.asyncio
    async def test_init_keys_from_redis_cache(self, redis_repo, sample_key_pair):
        """测试从Redis缓存获取密钥"""
        private_key, public_key = sample_key_pair

        # 模拟Redis中已有缓存的密钥
        def mock_get(key):
            if key == "jwt_keys:private_key":
                return private_key
            elif key == "jwt_keys:public_key":
                return public_key
            return None

        redis_repo.get.side_effect = mock_get

        jwt_manager = JWTManager(redis_repo=redis_repo)
        await jwt_manager._ensure_keys_initialized()

        assert jwt_manager.private_key == private_key
        assert jwt_manager.public_key == public_key

        # 验证没有尝试获取锁（因为缓存中有有效密钥）
        redis_repo.set.assert_not_called()

    @pytest.mark.asyncio
    async def test_generate_new_keys_with_lock(self, redis_repo):
        """测试使用分布式锁生成新密钥"""
        # 模拟Redis中没有缓存的密钥
        redis_repo.get.return_value = None
        redis_repo.set.return_value = True  # 锁获取成功

        jwt_manager = JWTManager(redis_repo=redis_repo)
        await jwt_manager._ensure_keys_initialized()

        # 验证密钥已生成
        assert jwt_manager.private_key is not None
        assert jwt_manager.public_key is not None
        assert "BEGIN PRIVATE KEY" in jwt_manager.private_key
        assert "BEGIN PUBLIC KEY" in jwt_manager.public_key

        # 验证密钥已保存到Redis
        assert redis_repo.set.call_count >= 2  # 至少调用了两次set（锁+密钥）

    @pytest.mark.asyncio
    async def test_concurrent_key_initialization(self, redis_repo):
        """测试并发密钥初始化"""
        # 模拟第一个实例获取锁失败，第二个实例成功
        lock_call_count = 0

        def mock_set(key, value, ttl=None, nx=False):
            nonlocal lock_call_count
            if "init_lock" in key and nx:
                lock_call_count += 1
                return lock_call_count == 1  # 第一次失败，第二次成功
            return True

        redis_repo.set.side_effect = mock_set
        redis_repo.get.return_value = None

        jwt_manager = JWTManager(redis_repo=redis_repo)

        # 模拟并发初始化
        tasks = [
            jwt_manager._ensure_keys_initialized(),
            jwt_manager._ensure_keys_initialized()
        ]

        await asyncio.gather(*tasks)

        # 验证只初始化了一次
        assert jwt_manager._keys_initialized is True
        assert jwt_manager.private_key is not None

    @pytest.mark.asyncio
    async def test_key_validation_success(self, redis_repo, sample_key_pair):
        """测试密钥验证成功"""
        private_key, public_key = sample_key_pair

        jwt_manager = JWTManager(redis_repo=redis_repo)

        # 应该不抛出异常
        await jwt_manager._validate_key_pair(private_key, public_key)

    @pytest.mark.asyncio
    async def test_key_validation_failure(self, redis_repo):
        """测试密钥验证失败"""
        jwt_manager = JWTManager(redis_repo=redis_repo)

        invalid_private = "invalid_private_key"
        invalid_public = "invalid_public_key"

        with pytest.raises(ValueError, match="Invalid key pair"):
            await jwt_manager._validate_key_pair(invalid_private, invalid_public)

    @pytest.mark.asyncio
    async def test_redis_lock_acquire_and_release(self, redis_repo):
        """测试Redis分布式锁的获取和释放"""
        jwt_manager = JWTManager(redis_repo=redis_repo)

        # 测试锁获取成功
        redis_repo.set.return_value = True
        lock_acquired = await jwt_manager._acquire_redis_lock("test_lock", 30)
        assert lock_acquired is True

        # 测试锁释放
        redis_repo.get.return_value = jwt_manager._current_lock_value
        lock_released = await jwt_manager._release_redis_lock("test_lock")
        assert lock_released is True

    @pytest.mark.asyncio
    async def test_redis_lock_acquire_failure(self, redis_repo):
        """测试Redis分布式锁获取失败"""
        jwt_manager = JWTManager(redis_repo=redis_repo)

        # 模拟锁获取失败
        redis_repo.set.return_value = False
        lock_acquired = await jwt_manager._acquire_redis_lock("test_lock", 30)
        assert lock_acquired is False

    @pytest.mark.asyncio
    async def test_key_generation_logging(self, redis_repo):
        """测试密钥生成日志记录"""
        jwt_manager = JWTManager(redis_repo=redis_repo)

        await jwt_manager._log_key_generation()

        # 验证日志记录调用
        redis_repo.lpush.assert_called_once()
        redis_repo.ltrim.assert_called_once()

        # 验证日志内容
        call_args = redis_repo.lpush.call_args
        assert call_args[0][0] == "jwt_key_events"
        log_entry = call_args[0][1]
        assert log_entry["event"] == "jwt_keys_generated"
        assert log_entry["algorithm"] == "RS256"
        assert log_entry["key_size"] == 2048

    @pytest.mark.asyncio
    async def test_token_generation_with_key_initialization(self, redis_repo):
        """测试令牌生成时的密钥初始化"""
        jwt_manager = JWTManager(redis_repo=redis_repo)

        # 模拟生成令牌
        token_pair = await jwt_manager.generate_token_pair(
            user_id="test_user",
            tenant_id="test_tenant",
            session_id="test_session",
            roles=["user"],
            permissions=["read"]
        )

        # 验证令牌生成成功
        assert token_pair.access_token is not None
        assert token_pair.refresh_token is not None

        # 验证密钥已初始化
        assert jwt_manager._keys_initialized is True
        assert jwt_manager.private_key is not None
        assert jwt_manager.public_key is not None

    @pytest.mark.asyncio
    async def test_token_verification_with_key_initialization(self, redis_repo):
        """测试令牌验证时的密钥初始化"""
        jwt_manager = JWTManager(redis_repo=redis_repo)

        # 先生成一个令牌
        token_pair = await jwt_manager.generate_token_pair(
            user_id="test_user",
            tenant_id="test_tenant",
            session_id="test_session",
            roles=["user"],
            permissions=["read"]
        )

        # 模拟会话验证成功
        redis_repo.get.return_value = {"session_id": "test_session"}

        # 验证令牌
        payload = await jwt_manager.verify_access_token(token_pair.access_token)

        # 验证结果
        assert payload is not None
        assert payload.user_id == "test_user"
        assert payload.tenant_id == "test_tenant"