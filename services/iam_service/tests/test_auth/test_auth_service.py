"""
认证服务测试

测试认证服务的各项功能
"""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from services.iam_service.services.auth_service import AuthService
from domain_common.models import CommonStatus
from domain_common.models.iam_models import (
    User, Tenant, Role, Permission, UserRole, RolePermission,
    UserSessionHistory, UserMFA, VerificationCode, PasswordHistory, AuditLog
)
from commonlib.exceptions.exceptions import (
    ValidationError, NotFoundError, AuthenticationError, BusinessError
)


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    session = AsyncMock()
    return session


@pytest.fixture
def mock_redis_repo():
    """模拟Redis仓库"""
    redis_repo = AsyncMock()
    return redis_repo


@pytest.fixture
def mock_jwt_manager():
    """模拟JWT管理器"""
    jwt_manager = MagicMock()
    jwt_manager.generate_token_pair.return_value = {
        "access_token": "mock_access_token",
        "refresh_token": "mock_refresh_token",
        "expires_in": 7200,
        "refresh_expires_in": 86400
    }
    return jwt_manager


@pytest.fixture
def mock_session_manager():
    """模拟会话管理器"""
    session_manager = AsyncMock()
    session_manager.create_session.return_value = MagicMock(session_id="session_123")
    return session_manager


@pytest.fixture
def mock_security_utils():
    """模拟安全工具"""
    security_utils = MagicMock()
    security_utils.verify_password.return_value = True
    security_utils.hash_password.return_value = "hashed_password"
    security_utils.verify_totp.return_value = True
    return security_utils


@pytest.fixture
def auth_service(mock_session, mock_redis_repo, mock_jwt_manager, mock_session_manager, mock_security_utils):
    """创建认证服务实例"""
    service = AuthService(
        session=mock_session,
        redis_repo=mock_redis_repo,
        user_model=User,
        tenant_model=Tenant,
        role_model=Role,
        permission_model=Permission,
        user_role_model=UserRole,
        role_permission_model=RolePermission,
        user_session_history_model=UserSessionHistory,
        user_mfa_model=UserMFA,
        verification_code_model=VerificationCode,
        password_history_model=PasswordHistory,
        audit_log_model=AuditLog
    )
    
    # 注入模拟对象
    service.jwt_manager = mock_jwt_manager
    service.session_manager = mock_session_manager
    service.security_utils = mock_security_utils
    
    return service


class TestAuthService:
    """认证服务测试类"""

    @pytest.mark.asyncio
    async def test_login_success(self, auth_service, mock_session):
        """测试成功登录"""
        # 准备测试数据
        tenant_id = "tenant_123"
        identifier = "testuser"
        credential = "password123"
        
        # 模拟租户存在
        mock_tenant = MagicMock()
        mock_tenant.status = CommonStatus.ACTIVE
        auth_service._get_tenant_by_id = AsyncMock(return_value=mock_tenant)
        
        # 模拟用户存在
        mock_user = MagicMock()
        mock_user.user_id = "user_123"
        mock_user.username = "testuser"
        mock_user.email = "<EMAIL>"
        mock_user.tenant_id = tenant_id
        mock_user.status = CommonStatus.ACTIVE
        mock_user.mfa_enabled = False
        mock_user.password_expires_at = None
        auth_service._find_user_by_identifier = AsyncMock(return_value=mock_user)
        
        # 模拟验证成功
        auth_service._verify_credential = AsyncMock(return_value=True)
        auth_service._check_login_rate_limit = AsyncMock()
        auth_service._create_user_session = AsyncMock(return_value="session_123")
        auth_service._update_user_login_info = AsyncMock()
        auth_service._generate_security_warnings = AsyncMock(return_value=[])
        auth_service._calculate_password_expires_days = AsyncMock(return_value=None)
        
        # 执行测试
        result = await auth_service.login(
            tenant_id=tenant_id,
            login_type="username",
            identifier=identifier,
            credential=credential,
            remember_me=False
        )
        
        # 验证结果
        assert result["access_token"] == "mock_access_token"
        assert result["refresh_token"] == "mock_refresh_token"
        assert result["session_id"] == "session_123"
        assert result["user_info"]["user_id"] == "user_123"
        assert result["user_info"]["username"] == "testuser"

    @pytest.mark.asyncio
    async def test_login_invalid_credentials(self, auth_service):
        """测试登录凭证错误"""
        # 模拟租户存在
        mock_tenant = MagicMock()
        mock_tenant.status = CommonStatus.ACTIVE
        auth_service._get_tenant_by_id = AsyncMock(return_value=mock_tenant)
        
        # 模拟用户存在但密码错误
        mock_user = MagicMock()
        mock_user.status = CommonStatus.ACTIVE
        auth_service._find_user_by_identifier = AsyncMock(return_value=mock_user)
        auth_service._verify_credential = AsyncMock(return_value=False)
        auth_service._handle_login_failure = AsyncMock()
        
        # 执行测试
        with pytest.raises(AuthenticationError, match="登录凭证错误"):
            await auth_service.login(
                tenant_id="tenant_123",
                login_type="username",
                identifier="testuser",
                credential="wrong_password",
                remember_me=False
            )

    @pytest.mark.asyncio
    async def test_logout_success(self, auth_service, mock_session_manager):
        """测试成功登出"""
        # 准备测试数据
        tenant_id = "tenant_123"
        session_id = "session_123"
        
        # 模拟会话存在
        mock_session_info = MagicMock()
        mock_session_info.user_id = "user_123"
        mock_session_manager.get_session.return_value = mock_session_info
        mock_session_manager.terminate_session.return_value = True
        mock_session_manager.get_user_active_sessions_count.return_value = 2
        
        # 执行测试
        result = await auth_service.logout(
            tenant_id=tenant_id,
            session_id=session_id,
            logout_all_devices=False
        )
        
        # 验证结果
        assert result["user_id"] == "user_123"
        assert result["logged_out_sessions"] == 1
        assert result["remaining_sessions"] == 2
        assert "logout_time" in result

    @pytest.mark.asyncio
    async def test_change_password_success(self, auth_service, mock_session):
        """测试成功修改密码"""
        # 准备测试数据
        tenant_id = "tenant_123"
        user_id = "user_123"
        old_password = "old_password"
        new_password = "new_password123"
        
        # 模拟用户存在
        mock_user = MagicMock()
        mock_user.user_id = user_id
        mock_user.tenant_id = tenant_id
        mock_user.password_hash = "old_hash"
        auth_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        # 模拟密码验证成功
        auth_service.security_utils.verify_password.return_value = True
        auth_service._validate_password_policy = AsyncMock(return_value=True)
        auth_service.security_utils.hash_password.return_value = "new_hash"
        
        # 执行测试
        result = await auth_service.change_password(
            tenant_id=tenant_id,
            user_id=user_id,
            old_password=old_password,
            new_password=new_password,
            logout_other_sessions=True
        )
        
        # 验证结果
        assert result["user_id"] == user_id
        assert "password_changed_at" in result
        assert "password_expires_at" in result
        
        # 验证密码已更新
        assert mock_user.password_hash == "new_hash"

    @pytest.mark.asyncio
    async def test_setup_mfa_success(self, auth_service, mock_redis_repo):
        """测试成功设置MFA"""
        # 准备测试数据
        tenant_id = "tenant_123"
        user_id = "user_123"
        mfa_type = "totp"
        
        # 模拟用户存在且未启用MFA
        mock_user = MagicMock()
        mock_user.user_id = user_id
        mock_user.tenant_id = tenant_id
        mock_user.mfa_enabled = False
        mock_user.email = "<EMAIL>"
        auth_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        # 模拟Redis设置
        mock_redis_repo.set = AsyncMock()
        
        # 执行测试
        result = await auth_service.setup_mfa(
            tenant_id=tenant_id,
            user_id=user_id,
            mfa_type=mfa_type
        )
        
        # 验证结果
        assert "secret_key" in result
        assert "qr_code_url" in result
        assert "backup_codes" in result
        assert "setup_token" in result
        assert len(result["backup_codes"]) == 10

    @pytest.mark.asyncio
    async def test_disable_mfa_success(self, auth_service, mock_session):
        """测试成功禁用MFA"""
        # 准备测试数据
        tenant_id = "tenant_123"
        user_id = "user_123"
        password = "user_password"
        
        # 模拟用户存在且启用了MFA
        mock_user = MagicMock()
        mock_user.user_id = user_id
        mock_user.tenant_id = tenant_id
        mock_user.mfa_enabled = True
        mock_user.password_hash = "hashed_password"
        auth_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        # 模拟MFA配置存在
        mock_mfa_config = MagicMock()
        mock_mfa_config.mfa_type = "totp"
        mock_mfa_config.secret_key = "test_secret"
        auth_service._get_user_mfa = AsyncMock(return_value=mock_mfa_config)
        
        # 模拟密码验证成功
        auth_service.security_utils.verify_password.return_value = True
        auth_service._clear_backup_codes = AsyncMock()
        
        # 执行测试
        result = await auth_service.disable_mfa(
            tenant_id=tenant_id,
            user_id=user_id,
            password=password,
            reason="用户请求禁用"
        )
        
        # 验证结果
        assert result["user_id"] == user_id
        assert result["mfa_enabled"] is False
        assert result["reason"] == "用户请求禁用"
        assert "disabled_at" in result

    @pytest.mark.asyncio
    async def test_verify_code_success(self, auth_service, mock_redis_repo):
        """测试成功验证验证码"""
        # 准备测试数据
        code_id = "code_123"
        verification_code = "123456"
        code_type = "sms"
        
        # 模拟验证码存在且有效
        code_data = {
            "code": verification_code,
            "type": code_type,
            "created_at": datetime.utcnow().isoformat(),
            "expires_at": (datetime.utcnow() + timedelta(minutes=5)).isoformat()
        }
        mock_redis_repo.get.return_value = code_data
        mock_redis_repo.delete = AsyncMock()
        
        # 执行测试
        result = await auth_service.verify_code(
            code_id=code_id,
            verification_code=verification_code,
            code_type=code_type
        )
        
        # 验证结果
        assert result["code_id"] == code_id
        assert result["verified"] is True
        assert "verified_at" in result
        
        # 验证验证码被删除
        mock_redis_repo.delete.assert_called_once_with(f"verification_code:{code_id}")


if __name__ == "__main__":
    pytest.main([__file__])
