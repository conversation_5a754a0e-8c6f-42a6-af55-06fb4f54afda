"""
系统服务测试

测试系统服务的各项功能
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from services.iam_service.services.system_service import SystemService
from domain_common.models.iam_models import (
    User, Tenant, AuditLog, SystemConfig, CacheKey,
    SecurityPolicy, SecurityEvent
)
from commonlib.exceptions.exceptions import (
    ValidationError, NotFoundError, BusinessError
)


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    session = AsyncMock()
    return session


@pytest.fixture
def mock_redis_repo():
    """模拟Redis仓库"""
    redis_repo = AsyncMock()
    return redis_repo


@pytest.fixture
def system_service(mock_session, mock_redis_repo):
    """创建系统服务实例"""
    return SystemService(
        session=mock_session,
        redis_repo=mock_redis_repo,
        user_model=User,
        tenant_model=Tenant,
        audit_log_model=AuditLog,
        system_config_model=SystemConfig,
        cache_key_model=CacheKey,
        security_policy_model=SecurityPolicy,
        security_event_model=SecurityEvent
    )


class TestSystemService:
    """系统服务测试类"""

    @pytest.mark.asyncio
    async def test_query_metrics_success(self, system_service, mock_session, mock_redis_repo):
        """测试成功查询系统指标"""
        # 模拟Redis会话数据
        mock_redis_repo.scan_keys.return_value = ["session:1", "session:2", "session:3"]
        mock_redis_repo.get.return_value = "150"  # API请求数
        
        # 模拟租户数据
        mock_tenant = MagicMock()
        mock_tenant.tenant_id = "tenant_123"
        mock_session.execute.return_value.scalars.return_value.all.return_value = [mock_tenant]
        
        # 模拟用户数量查询
        mock_session.execute.return_value.scalar.return_value = 25
        
        # 执行测试
        result = await system_service.query_metrics(
            time_range="1h",
            metric_types=["system", "tenants"]
        )
        
        # 验证结果
        assert "system_metrics" in result
        assert "tenant_metrics" in result
        assert "timestamp" in result
        assert "time_range" in result
        
        # 验证系统指标
        system_metrics = result["system_metrics"]
        assert "cpu_usage" in system_metrics
        assert "memory_usage" in system_metrics
        assert "active_sessions" in system_metrics
        
        # 验证时间范围
        time_range = result["time_range"]
        assert time_range["duration"] == "1h"

    @pytest.mark.asyncio
    async def test_query_metrics_invalid_time_range(self, system_service):
        """测试查询指标时使用无效时间范围"""
        with pytest.raises(ValidationError, match="不支持的时间范围"):
            await system_service.query_metrics(time_range="invalid")

    @pytest.mark.asyncio
    async def test_query_metrics_invalid_metric_types(self, system_service):
        """测试查询指标时使用无效指标类型"""
        with pytest.raises(ValidationError, match="不支持的指标类型"):
            await system_service.query_metrics(metric_types=["invalid_type"])

    @pytest.mark.asyncio
    async def test_query_logs_success(self, system_service):
        """测试成功查询系统日志"""
        # 执行测试
        result = await system_service.query_logs(
            tenant_id="tenant_123",
            level="INFO",
            service="iam_service",
            limit=50
        )
        
        # 验证结果
        assert "logs" in result
        assert "total" in result
        assert "has_more" in result
        assert "query_params" in result
        
        # 验证查询参数
        query_params = result["query_params"]
        assert query_params["tenant_id"] == "tenant_123"
        assert query_params["level"] == "INFO"
        assert query_params["service"] == "iam_service"
        assert query_params["limit"] == 50

    @pytest.mark.asyncio
    async def test_query_logs_invalid_level(self, system_service):
        """测试查询日志时使用无效日志级别"""
        with pytest.raises(ValidationError, match="不支持的日志级别"):
            await system_service.query_logs(level="INVALID")

    @pytest.mark.asyncio
    async def test_query_logs_invalid_limit(self, system_service):
        """测试查询日志时使用无效分页参数"""
        with pytest.raises(ValidationError, match="每页数量不能超过1000"):
            await system_service.query_logs(limit=1001)

    @pytest.mark.asyncio
    async def test_query_audit_logs_success(self, system_service, mock_session):
        """测试成功查询审计日志"""
        # 准备测试数据
        tenant_id = "tenant_123"
        
        # 模拟审计日志数据
        mock_log = MagicMock()
        mock_log.id = 1
        mock_log.tenant_id = tenant_id
        mock_log.user_id = "user_123"
        mock_log.action = "LOGIN"
        mock_log.resource_type = "USER"
        mock_log.resource_id = None
        mock_log.details = {"ip": "*************"}
        mock_log.ip_address = "*************"
        mock_log.user_agent = "Mozilla/5.0"
        mock_log.created_at = datetime.utcnow()
        
        # 模拟数据库查询
        mock_session.execute.return_value.scalar.return_value = 1  # 总数
        mock_session.execute.return_value.scalars.return_value.all.return_value = [mock_log]
        
        # 模拟用户查询
        mock_user = MagicMock()
        mock_user.username = "testuser"
        system_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        # 执行测试
        result = await system_service.query_audit_logs(
            tenant_id=tenant_id,
            user_id="user_123",
            action="LOGIN",
            limit=50
        )
        
        # 验证结果
        assert result["total"] == 1
        assert len(result["audit_logs"]) == 1
        assert result["has_more"] is False
        
        # 验证审计日志数据
        audit_log = result["audit_logs"][0]
        assert audit_log["log_id"] == "1"
        assert audit_log["tenant_id"] == tenant_id
        assert audit_log["action"] == "LOGIN"
        assert audit_log["username"] == "testuser"

    @pytest.mark.asyncio
    async def test_query_audit_logs_invalid_limit(self, system_service):
        """测试查询审计日志时使用无效分页参数"""
        with pytest.raises(ValidationError, match="每页数量不能超过200"):
            await system_service.query_audit_logs(
                tenant_id="tenant_123",
                limit=201
            )

    @pytest.mark.asyncio
    async def test_query_audit_logs_invalid_cursor(self, system_service):
        """测试查询审计日志时使用无效游标"""
        with pytest.raises(ValidationError, match="无效的游标格式"):
            await system_service.query_audit_logs(
                tenant_id="tenant_123",
                cursor="invalid_cursor"
            )

    @pytest.mark.asyncio
    async def test_get_system_config_success(self, system_service, mock_session):
        """测试成功获取系统配置"""
        # 准备测试数据
        config_key = "max_upload_size"
        
        # 模拟配置数据
        mock_config = MagicMock()
        mock_config.config_id = "config_123"
        mock_config.config_key = config_key
        mock_config.config_value = "100MB"
        mock_config.config_type = "string"
        mock_config.description = "最大上传文件大小"
        mock_config.is_public = True
        mock_config.version = "1.0"
        mock_config.created_at = datetime.utcnow()
        mock_config.updated_at = None
        
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_config
        
        # 执行测试
        result = await system_service.get_system_config(config_key)
        
        # 验证结果
        assert result["config_key"] == config_key
        assert result["config_value"] == "100MB"
        assert result["config_type"] == "string"
        assert result["description"] == "最大上传文件大小"
        assert result["is_public"] is True

    @pytest.mark.asyncio
    async def test_get_system_config_not_found(self, system_service, mock_session):
        """测试获取不存在的系统配置"""
        # 模拟配置不存在
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        
        with pytest.raises(NotFoundError, match="配置项不存在"):
            await system_service.get_system_config("nonexistent_key")

    @pytest.mark.asyncio
    async def test_set_system_config_new(self, system_service, mock_session):
        """测试设置新的系统配置"""
        # 模拟配置不存在（新建）
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        
        # 执行测试
        result = await system_service.set_system_config(
            config_key="new_config",
            config_value="test_value",
            config_type="string",
            description="测试配置",
            is_public=True,
            created_by="admin"
        )
        
        # 验证结果
        assert result["config_key"] == "new_config"
        assert result["config_value"] == "test_value"
        assert result["config_type"] == "string"
        assert result["description"] == "测试配置"
        assert result["is_public"] is True
        
        # 验证数据库操作
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_set_system_config_update(self, system_service, mock_session):
        """测试更新现有系统配置"""
        # 模拟配置已存在
        mock_config = MagicMock()
        mock_config.config_key = "existing_config"
        mock_config.config_value = "old_value"
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_config
        
        # 执行测试
        result = await system_service.set_system_config(
            config_key="existing_config",
            config_value="new_value",
            config_type="string",
            description="更新的配置"
        )
        
        # 验证配置被更新
        assert mock_config.config_value == "new_value"
        assert mock_config.description == "更新的配置"
        
        # 验证数据库操作
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_cache_statistics_success(self, system_service, mock_session):
        """测试成功获取缓存统计"""
        # 模拟缓存统计数据
        mock_stats = [
            MagicMock(cache_type="session", count=100, total_access=5000),
            MagicMock(cache_type="user", count=50, total_access=2000)
        ]
        mock_session.execute.return_value.all.return_value = mock_stats
        
        # 执行测试
        result = await system_service.get_cache_statistics()
        
        # 验证结果
        assert "cache_types" in result
        assert "redis_info" in result
        assert "timestamp" in result
        
        # 验证缓存类型统计
        cache_types = result["cache_types"]
        assert len(cache_types) == 2
        assert cache_types[0]["type"] == "session"
        assert cache_types[0]["key_count"] == 100
        assert cache_types[0]["total_access"] == 5000

    @pytest.mark.asyncio
    async def test_clear_cache_by_pattern(self, system_service, mock_redis_repo):
        """测试按模式清除缓存"""
        # 模拟Redis操作
        mock_redis_repo.scan_keys.return_value = ["session:1", "session:2", "session:3"]
        mock_redis_repo.delete = AsyncMock()
        
        # 执行测试
        result = await system_service.clear_cache(pattern="session:*")
        
        # 验证结果
        assert result["cleared_count"] == 3
        assert result["pattern"] == "session:*"
        assert "cleared_at" in result
        
        # 验证Redis操作
        mock_redis_repo.scan_keys.assert_called_once_with("session:*")
        mock_redis_repo.delete.assert_called_once_with("session:1", "session:2", "session:3")

    @pytest.mark.asyncio
    async def test_clear_cache_all(self, system_service, mock_redis_repo):
        """测试清除所有缓存"""
        # 模拟Redis操作
        mock_redis_repo.flushdb = AsyncMock()
        
        # 执行测试
        result = await system_service.clear_cache()
        
        # 验证结果
        assert result["cleared_count"] == -1  # 表示全部清除
        assert "cleared_at" in result
        
        # 验证Redis操作
        mock_redis_repo.flushdb.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
