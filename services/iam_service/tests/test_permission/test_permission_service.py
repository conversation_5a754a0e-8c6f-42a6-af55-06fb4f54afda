"""
权限服务测试

测试权限服务的各项功能
"""

import pytest
import uuid
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

from services.iam_service.services.permission_service import PermissionService
from domain_common.models import CommonStatus
from domain_common.models.iam_models import (
    User, Tenant, Role, Permission,
    UserRole, RolePermission, UserPolicy,
    PermissionPolicy, AuditLog
)
from commonlib.exceptions.exceptions import (
    ValidationError, DuplicateResourceError, NotFoundError,
    BusinessError
)


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    session = AsyncMock()
    return session


@pytest.fixture
def mock_redis_repo():
    """模拟Redis仓库"""
    redis_repo = AsyncMock()
    return redis_repo


@pytest.fixture
def permission_service(mock_session, mock_redis_repo):
    """创建权限服务实例"""
    return PermissionService(
        session=mock_session,
        redis_repo=mock_redis_repo,
        user_model=User,
        tenant_model=Tenant,
        role_model=Role,
        permission_model=Permission,
        user_role_model=UserRole,
        role_permission_model=RolePermission,
        user_policy_model=UserPolicy,
        permission_policy_model=PermissionPolicy,
        audit_log_model=AuditLog
    )


class TestPermissionService:
    """权限服务测试类"""

    @pytest.mark.asyncio
    async def test_create_permission_success(self, permission_service, mock_session):
        """测试成功创建权限"""
        # 准备测试数据
        tenant_id = "tenant_123"
        permission_name = "查看用户"
        permission_code = "user:read"
        resource_type = "user"
        action = "read"
        
        # 模拟租户存在
        mock_tenant = MagicMock()
        mock_tenant.status = CommonStatus.ACTIVE
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant
        
        # 模拟权限代码不存在（唯一性检查）
        mock_session.execute.return_value.scalar_one_or_none.side_effect = [
            mock_tenant,  # 租户查询
            None,  # 权限代码唯一性检查
        ]
        
        # 执行测试
        result = await permission_service.create_permission(
            permission_name=permission_name,
            permission_code=permission_code,
            description="查看用户信息的权限",
            resource_type=resource_type,
            action=action,
            tenant_id=tenant_id
        )
        
        # 验证结果
        assert result["permission_name"] == permission_name
        assert result["permission_code"] == permission_code
        assert result["resource_type"] == resource_type
        assert result["action"] == action
        assert result["status"] == CommonStatus.ACTIVE
        assert "permission_id" in result
        assert "created_at" in result
        
        # 验证数据库操作
        mock_session.add.assert_called()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_permission_duplicate_code(self, permission_service, mock_session):
        """测试创建重复权限代码"""
        # 准备测试数据
        tenant_id = "tenant_123"
        permission_code = "existing:permission"
        
        # 模拟租户存在
        mock_tenant = MagicMock()
        mock_tenant.status = CommonStatus.ACTIVE
        
        # 模拟权限代码已存在
        mock_existing_permission = MagicMock()
        mock_session.execute.return_value.scalar_one_or_none.side_effect = [
            mock_tenant,  # 租户查询
            mock_existing_permission,  # 权限代码已存在
        ]
        
        # 执行测试并验证异常
        with pytest.raises(DuplicateResourceError, match="权限代码已存在"):
            await permission_service.create_permission(
                permission_name="测试权限",
                permission_code=permission_code,
                description="测试权限描述",
                resource_type="test",
                action="read",
                tenant_id=tenant_id
            )

    @pytest.mark.asyncio
    async def test_list_permissions_with_search(self, permission_service, mock_session):
        """测试搜索权限列表"""
        # 准备测试数据
        tenant_id = "tenant_123"
        search = "用户"
        
        # 模拟查询结果
        mock_permissions = [
            MagicMock(
                permission_id="perm_1",
                permission_name="查看用户",
                permission_code="user:read",
                description="查看用户信息",
                resource="user",
                action="read",
                status=CommonStatus.ACTIVE,
                tenant_id=tenant_id,
                parent_permission_id=None,
                level=1,
                created_at=datetime.utcnow(),
                updated_at=None
            ),
            MagicMock(
                permission_id="perm_2",
                permission_name="编辑用户",
                permission_code="user:write",
                description="编辑用户信息",
                resource="user",
                action="write",
                status=CommonStatus.ACTIVE,
                tenant_id=tenant_id,
                parent_permission_id=None,
                level=1,
                created_at=datetime.utcnow(),
                updated_at=None
            )
        ]
        
        # 模拟数据库查询
        mock_session.execute.return_value.scalar.return_value = 2  # 总数
        mock_session.execute.return_value.scalars.return_value.all.return_value = mock_permissions
        
        # 执行测试
        result = await permission_service.list_permissions(
            tenant_id=tenant_id,
            search=search,
            limit=10
        )
        
        # 验证结果
        assert result["total"] == 2
        assert len(result["permissions"]) == 2
        assert result["has_more"] is False
        
        # 验证权限数据
        perm1 = result["permissions"][0]
        assert perm1["permission_id"] == "perm_1"
        assert perm1["permission_name"] == "查看用户"
        assert perm1["resource_type"] == "user"

    @pytest.mark.asyncio
    async def test_get_permission_detail_success(self, permission_service, mock_session):
        """测试获取权限详情成功"""
        # 准备测试数据
        permission_id = "perm_123"
        tenant_id = "tenant_123"
        
        # 模拟缓存未命中
        permission_service.redis_repo.get.return_value = None
        
        # 模拟权限存在
        mock_permission = MagicMock(
            permission_id=permission_id,
            permission_name="查看用户",
            permission_code="user:read",
            description="查看用户信息",
            resource="user",
            action="read",
            status=CommonStatus.ACTIVE,
            tenant_id=tenant_id,
            parent_permission_id=None,
            level=1,
            created_at=datetime.utcnow(),
            updated_at=None
        )
        
        # 模拟子权限查询
        mock_children = []
        
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_permission
        mock_session.execute.return_value.scalars.return_value.all.return_value = mock_children
        
        # 执行测试
        result = await permission_service.get_permission_detail(
            permission_id=permission_id,
            tenant_id=tenant_id
        )
        
        # 验证结果
        assert result["permission_id"] == permission_id
        assert result["permission_name"] == "查看用户"
        assert result["resource_type"] == "user"
        assert result["children"] == []
        
        # 验证缓存操作
        permission_service.redis_repo.set.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_permission_tree_success(self, permission_service, mock_session):
        """测试获取权限树成功"""
        # 准备测试数据
        tenant_id = "tenant_123"
        
        # 模拟缓存未命中
        permission_service.redis_repo.get.return_value = None
        
        # 模拟权限数据（父子关系）
        mock_permissions = [
            MagicMock(
                permission_id="perm_parent",
                permission_name="用户管理",
                permission_code="user:manage",
                description="用户管理权限",
                resource="user",
                action="manage",
                status=CommonStatus.ACTIVE,
                tenant_id=tenant_id,
                parent_permission_id=None,
                level=1,
                created_at=datetime.utcnow(),
                updated_at=None
            ),
            MagicMock(
                permission_id="perm_child",
                permission_name="查看用户",
                permission_code="user:read",
                description="查看用户信息",
                resource="user",
                action="read",
                status=CommonStatus.ACTIVE,
                tenant_id=tenant_id,
                parent_permission_id="perm_parent",
                level=2,
                created_at=datetime.utcnow(),
                updated_at=None
            )
        ]
        
        mock_session.execute.return_value.scalars.return_value.all.return_value = mock_permissions
        
        # 执行测试
        result = await permission_service.get_permission_tree(tenant_id=tenant_id)
        
        # 验证结果
        assert result["total"] == 2
        assert len(result["tree"]) == 1  # 只有一个根权限
        
        root_permission = result["tree"][0]
        assert root_permission["permission_id"] == "perm_parent"
        assert len(root_permission["children"]) == 1
        
        child_permission = root_permission["children"][0]
        assert child_permission["permission_id"] == "perm_child"
        assert child_permission["parent_id"] == "perm_parent"
        
        # 验证缓存操作
        permission_service.redis_repo.set.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_permission_with_children_force(self, permission_service, mock_session):
        """测试强制删除有子权限的权限"""
        # 准备测试数据
        permission_id = "perm_parent"
        tenant_id = "tenant_123"
        
        # 模拟权限存在
        mock_permission = MagicMock(
            permission_id=permission_id,
            permission_name="用户管理",
            permission_code="user:manage",
            tenant_id=tenant_id,
            meta_data={"is_system": False},
            status=CommonStatus.ACTIVE
        )
        mock_session.execute.return_value.scalar_one_or_none.return_value = mock_permission
        
        # 模拟有子权限
        permission_service._get_permission_children_count = AsyncMock(return_value=2)
        permission_service._check_permission_usage = AsyncMock(return_value={"role_count": 0, "policy_count": 0})
        permission_service._force_delete_permission_associations = AsyncMock(return_value={"roles_unassigned": 0})
        permission_service._delete_permission_children = AsyncMock()
        
        # 执行测试
        result = await permission_service.delete_permission(
            permission_id=permission_id,
            tenant_id=tenant_id,
            force=True
        )
        
        # 验证结果
        assert result["success"] is True
        assert result["permission_id"] == permission_id
        
        # 验证删除子权限被调用
        permission_service._delete_permission_children.assert_called_once_with(permission_id)
        
        # 验证数据库操作
        mock_session.commit.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
