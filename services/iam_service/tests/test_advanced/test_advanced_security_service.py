"""
高级安全服务测试

测试高级安全服务的各项功能
"""

import pytest
import uuid
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

from services.iam_service.services.advanced_security_service import AdvancedSecurityService
from domain_common.models.iam_models import (
    User, Tenant, UserMFA, SecurityPolicy, SecurityEvent, AuditLog
)
from commonlib.exceptions.exceptions import (
    ValidationError, NotFoundError, AuthenticationError, BusinessError
)


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    session = AsyncMock()
    return session


@pytest.fixture
def mock_redis_repo():
    """模拟Redis仓库"""
    redis_repo = AsyncMock()
    return redis_repo


@pytest.fixture
def security_service(mock_session, mock_redis_repo):
    """创建高级安全服务实例"""
    return AdvancedSecurityService(
        session=mock_session,
        redis_repo=mock_redis_repo,
        user_model=User,
        tenant_model=Tenant,
        user_mfa_model=UserMFA,
        security_policy_model=SecurityPolicy,
        security_event_model=SecurityEvent,
        audit_log_model=AuditLog
    )


class TestAdvancedSecurityService:
    """高级安全服务测试类"""

    @pytest.mark.asyncio
    async def test_setup_mfa_totp_success(self, security_service, mock_session, mock_redis_repo):
        """测试成功设置TOTP MFA"""
        # 准备测试数据
        tenant_id = "tenant_123"
        user_id = "user_123"
        mfa_type = "totp"
        
        # 模拟用户存在
        mock_user = MagicMock()
        mock_user.tenant_id = tenant_id
        mock_user.email = "<EMAIL>"
        mock_user.username = "testuser"
        security_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        # 模拟Redis设置
        mock_redis_repo.set = AsyncMock()
        
        # 执行测试
        result = await security_service.setup_mfa(
            tenant_id=tenant_id,
            user_id=user_id,
            mfa_type=mfa_type
        )
        
        # 验证结果
        assert "setup_token" in result
        assert "qr_code_url" in result
        assert "secret_key" in result
        assert "backup_codes" in result
        assert len(result["backup_codes"]) == 10
        assert "expires_at" in result
        
        # 验证Redis操作
        mock_redis_repo.set.assert_called_once()

    @pytest.mark.asyncio
    async def test_setup_mfa_invalid_type(self, security_service):
        """测试设置无效的MFA类型"""
        with pytest.raises(ValidationError, match="不支持的MFA类型"):
            await security_service.setup_mfa(
                tenant_id="tenant_123",
                user_id="user_123",
                mfa_type="invalid_type"
            )

    @pytest.mark.asyncio
    async def test_setup_mfa_sms_without_phone(self, security_service):
        """测试设置SMS MFA但未提供手机号"""
        with pytest.raises(ValidationError, match="SMS MFA需要提供手机号"):
            await security_service.setup_mfa(
                tenant_id="tenant_123",
                user_id="user_123",
                mfa_type="sms"
            )

    @pytest.mark.asyncio
    async def test_verify_mfa_setup_success(self, security_service, mock_session, mock_redis_repo):
        """测试成功验证MFA设置"""
        # 准备测试数据
        tenant_id = "tenant_123"
        user_id = "user_123"
        setup_token = "setup_token_123"
        verification_code = "123456"
        
        # 模拟Redis中的设置信息
        setup_info = {
            "user_id": user_id,
            "tenant_id": tenant_id,
            "mfa_type": "totp",
            "mfa_config": {"secret": "test_secret"},
            "backup_codes": ["ABCD-1234", "EFGH-5678"],
            "created_at": datetime.now().isoformat()
        }
        mock_redis_repo.get.return_value = setup_info
        mock_redis_repo.delete = AsyncMock()
        
        # 模拟用户存在
        mock_user = MagicMock()
        mock_user.username = "testuser"
        security_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        # 模拟MFA不存在（新建）
        security_service._get_user_mfa = AsyncMock(return_value=None)
        
        # 模拟TOTP验证成功
        security_service._verify_totp = MagicMock(return_value=True)
        
        # 模拟存储备用恢复码
        security_service._store_backup_codes = AsyncMock()
        
        # 模拟创建安全事件
        security_service._create_security_event = AsyncMock()
        
        # 执行测试
        result = await security_service.verify_mfa_setup(
            tenant_id=tenant_id,
            user_id=user_id,
            setup_token=setup_token,
            verification_code=verification_code
        )
        
        # 验证结果
        assert result["user_id"] == user_id
        assert result["mfa_enabled"] is True
        assert result["mfa_type"] == "totp"
        assert "enabled_at" in result
        
        # 验证数据库操作
        mock_session.add.assert_called()
        mock_session.commit.assert_called_once()
        
        # 验证Redis操作
        mock_redis_repo.delete.assert_called_once()

    @pytest.mark.asyncio
    async def test_verify_mfa_setup_invalid_token(self, security_service, mock_redis_repo):
        """测试验证无效的设置令牌"""
        # 模拟Redis中没有设置信息
        mock_redis_repo.get.return_value = None
        
        with pytest.raises(NotFoundError, match="设置令牌不存在或已过期"):
            await security_service.verify_mfa_setup(
                tenant_id="tenant_123",
                user_id="user_123",
                setup_token="invalid_token",
                verification_code="123456"
            )

    @pytest.mark.asyncio
    async def test_verify_mfa_setup_wrong_code(self, security_service, mock_redis_repo):
        """测试验证错误的验证码"""
        # 准备测试数据
        setup_info = {
            "user_id": "user_123",
            "tenant_id": "tenant_123",
            "mfa_type": "totp",
            "mfa_config": {"secret": "test_secret"},
            "backup_codes": ["ABCD-1234"],
            "created_at": datetime.now().isoformat()
        }
        mock_redis_repo.get.return_value = setup_info
        
        # 模拟TOTP验证失败
        security_service._verify_totp = MagicMock(return_value=False)
        
        with pytest.raises(AuthenticationError, match="TOTP验证码错误"):
            await security_service.verify_mfa_setup(
                tenant_id="tenant_123",
                user_id="user_123",
                setup_token="setup_token_123",
                verification_code="wrong_code"
            )

    @pytest.mark.asyncio
    async def test_disable_mfa_success(self, security_service, mock_session):
        """测试成功禁用MFA"""
        # 准备测试数据
        tenant_id = "tenant_123"
        user_id = "user_123"
        verification_code = "123456"
        reason = "用户请求禁用"
        
        # 模拟用户存在且启用了MFA
        mock_user = MagicMock()
        mock_user.tenant_id = tenant_id
        mock_user.mfa_enabled = True
        mock_user.username = "testuser"
        security_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        # 模拟MFA配置存在
        mock_user_mfa = MagicMock()
        mock_user_mfa.mfa_type = "totp"
        mock_user_mfa.secret_key = "test_secret"
        mock_user_mfa.backup_codes = {}
        security_service._get_user_mfa = AsyncMock(return_value=mock_user_mfa)
        
        # 模拟TOTP验证成功
        security_service._verify_totp = MagicMock(return_value=True)
        
        # 模拟清除备用恢复码
        security_service._clear_backup_codes = AsyncMock()
        
        # 模拟创建安全事件
        security_service._create_security_event = AsyncMock()
        
        # 执行测试
        result = await security_service.disable_mfa(
            tenant_id=tenant_id,
            user_id=user_id,
            verification_code=verification_code,
            reason=reason
        )
        
        # 验证结果
        assert result["user_id"] == user_id
        assert result["mfa_enabled"] is False
        assert result["reason"] == reason
        assert "disabled_at" in result
        
        # 验证数据库操作
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_backup_codes_success(self, security_service):
        """测试成功生成备用恢复码"""
        # 准备测试数据
        tenant_id = "tenant_123"
        user_id = "user_123"
        count = 8
        
        # 模拟用户存在且启用了MFA
        mock_user = MagicMock()
        mock_user.tenant_id = tenant_id
        mock_user.mfa_enabled = True
        security_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        # 模拟存储备用恢复码
        security_service._store_backup_codes = AsyncMock()
        
        # 执行测试
        result = await security_service.generate_backup_codes(
            tenant_id=tenant_id,
            user_id=user_id,
            count=count
        )
        
        # 验证结果
        assert result["user_id"] == user_id
        assert len(result["backup_codes"]) == count
        assert "generated_at" in result
        assert "expires_at" in result
        
        # 验证备用码格式（XXXX-XXXX）
        for code in result["backup_codes"]:
            assert len(code) == 9  # 4 + 1 + 4
            assert code[4] == "-"

    @pytest.mark.asyncio
    async def test_set_security_policy_success(self, security_service, mock_session):
        """测试成功设置安全策略"""
        # 准备测试数据
        tenant_id = "tenant_123"
        policy_name = "password_policy"
        policy_config = {
            "min_length": 8,
            "require_uppercase": True,
            "require_lowercase": True,
            "require_numbers": True
        }
        
        # 模拟租户存在
        mock_tenant = MagicMock()
        security_service._get_tenant_by_id = AsyncMock(return_value=mock_tenant)
        
        # 模拟策略不存在（新建）
        security_service._get_security_policy = AsyncMock(return_value=None)
        
        # 模拟清除策略缓存
        security_service._clear_policy_cache = AsyncMock()
        
        # 执行测试
        result = await security_service.set_security_policy(
            tenant_id=tenant_id,
            policy_name=policy_name,
            policy_config=policy_config,
            enabled=True
        )
        
        # 验证结果
        assert "policy_id" in result
        assert result["policy_name"] == policy_name
        assert result["policy_config"] == policy_config
        assert result["enabled"] is True
        assert "updated_at" in result
        
        # 验证数据库操作
        mock_session.add.assert_called()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_query_security_events_success(self, security_service, mock_session):
        """测试成功查询安全事件"""
        # 准备测试数据
        tenant_id = "tenant_123"
        
        # 模拟安全事件数据
        mock_events = [
            MagicMock(
                event_id="event_1",
                tenant_id=tenant_id,
                event_type="login_anomaly",
                severity="high",
                title="异常登录",
                description="检测到异常登录行为",
                user_id="user_123",
                ip_address="*************",
                user_agent="Mozilla/5.0",
                details={"location": "unknown"},
                status="pending",
                assigned_to=None,
                notes=None,
                created_at=datetime.now(),
                updated_at=None
            )
        ]
        
        # 模拟数据库查询
        mock_session.execute.return_value.scalar.return_value = 1  # 总数
        mock_session.execute.return_value.scalars.return_value.all.return_value = mock_events
        
        # 模拟获取用户信息
        mock_user = MagicMock()
        mock_user.username = "testuser"
        security_service._get_user_by_id = AsyncMock(return_value=mock_user)
        
        # 执行测试
        result = await security_service.query_security_events(
            tenant_id=tenant_id,
            event_type="login_anomaly",
            severity="high",
            page=1,
            page_size=20
        )
        
        # 验证结果
        assert result["total"] == 1
        assert len(result["events"]) == 1
        assert result["page"] == 1
        assert result["page_size"] == 20
        assert result["has_next"] is False
        
        # 验证事件数据
        event = result["events"][0]
        assert event["event_id"] == "event_1"
        assert event["event_type"] == "login_anomaly"
        assert event["severity"] == "high"
        assert event["username"] == "testuser"


if __name__ == "__main__":
    pytest.main([__file__])
