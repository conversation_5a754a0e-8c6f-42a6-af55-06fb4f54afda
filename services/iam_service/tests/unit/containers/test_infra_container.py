"""
InfraContainer单元测试

测试基础设施容器的各项功能
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from dependency_injector import providers
from commonlib.core.containers.infra_container import InfraContainer
from commonlib.storages.connect_manager import ConnectionManager
from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.storages.persistence.mongodb.repository import MongoRepository
from domain_common.interface.infra_redis.decorators.cache import RedisCacheDecorator
from domain_common.interface.infra_redis.decorators.token_bucket import TokenBucketDecorator
from domain_common.interface.infra_redis.scripts.script import RedisScriptManager


class TestInfraContainer:
    """InfraContainer测试类"""

    @pytest.mark.unit
    def test_container_initialization(self):
        """测试容器初始化"""
        # Mock配置容器
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 验证容器具有必要的providers
        assert hasattr(container, 'config')
        assert hasattr(container, 'connector_registry')
        assert hasattr(container, 'connection_manager')
        assert hasattr(container, 'redis_client')
        assert hasattr(container, 'mysql_client')
        assert hasattr(container, 'postgres_client')
        assert hasattr(container, 'mongodb_client')
        assert hasattr(container, 'rabbitmq_client')

    @pytest.mark.unit
    def test_config_is_dependencies_container(self):
        """测试配置是依赖容器"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 验证config是DependenciesContainer
        assert isinstance(container.config, providers.DependenciesContainer)

    @pytest.mark.unit
    def test_connection_manager_is_singleton(self):
        """测试连接管理器是单例"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 验证connection_manager是Singleton
        assert isinstance(container.connection_manager, providers.Singleton)

    @pytest.mark.unit
    def test_database_clients_are_callable(self):
        """测试数据库客户端是Callable"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 验证各种客户端都是Callable
        assert isinstance(container.redis_client, providers.Callable)
        assert isinstance(container.mysql_client, providers.Callable)
        assert isinstance(container.postgres_client, providers.Callable)
        assert isinstance(container.mongodb_client, providers.Callable)
        assert isinstance(container.rabbitmq_client, providers.Callable)

    @pytest.mark.unit
    def test_repositories_are_factory(self):
        """测试仓库是Factory"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 验证仓库都是Factory
        assert isinstance(container.decorator_redis_repo, providers.Factory)
        assert isinstance(container.mongodb_repo_factory, providers.Factory)

    @pytest.mark.unit
    def test_redis_extensions_are_factory(self):
        """测试Redis扩展是Factory"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 验证Redis扩展都是Factory
        assert isinstance(container.redis_script_manager, providers.Factory)
        assert isinstance(container.redis_cache_decorator, providers.Factory)
        assert isinstance(container.redis_token_bucket_decorator, providers.Factory)

    @pytest.mark.unit
    def test_session_providers_are_callable(self):
        """测试会话提供者是Callable"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 验证会话提供者都是Callable
        assert isinstance(container.mysql_session_provider, providers.Callable)
        assert isinstance(container.postgres_session_provider, providers.Callable)

    @pytest.mark.unit
    def test_client_aliases_exist(self):
        """测试客户端别名存在"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 验证向后兼容的别名存在
        assert hasattr(container, 'redis_clint')
        assert hasattr(container, 'mysql_clint')
        assert hasattr(container, 'mongodb_clint')
        assert hasattr(container, 'rabbitmq_clint')
        
        # 验证别名指向正确的provider
        assert container.redis_clint is container.redis_client
        assert container.mysql_clint is container.mysql_client
        assert container.mongodb_clint is container.mongodb_client
        assert container.rabbitmq_clint is container.rabbitmq_client

    @pytest.mark.unit
    def test_container_with_mocked_dependencies(self):
        """测试带Mock依赖的容器"""
        # Mock配置容器
        mock_config = MagicMock()
        mock_config.persistence_config = MagicMock()
        mock_config.connection_priority_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock连接管理器
        mock_connection_manager = MagicMock(spec=ConnectionManager)
        mock_redis_connector = AsyncMock()
        mock_postgres_connector = AsyncMock()
        
        mock_connection_manager.get_connector.side_effect = lambda name: {
            'redis': mock_redis_connector,
            'postgres': mock_postgres_connector
        }.get(name)
        
        container.connection_manager.override(mock_connection_manager)
        
        # 测试客户端获取
        redis_client = container.redis_client()
        postgres_client = container.postgres_client()
        
        assert redis_client is mock_redis_connector
        assert postgres_client is mock_postgres_connector

    @pytest.mark.unit
    def test_redis_repository_creation(self):
        """测试Redis仓库创建"""
        # Mock配置
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis客户端
        mock_redis_client = AsyncMock()
        container.redis_client.override(mock_redis_client)
        
        # 测试Redis仓库创建
        with patch.object(RedisRepository, '__init__', return_value=None) as mock_init:
            redis_repo = container.decorator_redis_repo()
            
            # 验证RedisRepository被正确初始化
            mock_init.assert_called_once()

    @pytest.mark.unit
    def test_mongodb_repository_creation(self):
        """测试MongoDB仓库创建"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock MongoDB客户端
        mock_mongodb_client = AsyncMock()
        container.mongodb_client.override(mock_mongodb_client)
        
        # 测试MongoDB仓库创建
        with patch.object(MongoRepository, '__init__', return_value=None) as mock_init:
            mongodb_repo = container.mongodb_repo_factory()
            
            # 验证MongoRepository被正确初始化
            mock_init.assert_called_once()

    @pytest.mark.unit
    def test_redis_script_manager_creation(self):
        """测试Redis脚本管理器创建"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis仓库
        mock_redis_repo = AsyncMock()
        container.decorator_redis_repo.override(mock_redis_repo)
        
        # 测试Redis脚本管理器创建
        with patch.object(RedisScriptManager, '__init__', return_value=None) as mock_init:
            script_manager = container.redis_script_manager()
            
            # 验证RedisScriptManager被正确初始化
            mock_init.assert_called_once()

    @pytest.mark.unit
    def test_redis_cache_decorator_creation(self):
        """测试Redis缓存装饰器创建"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis脚本管理器
        mock_script_manager = MagicMock()
        container.redis_script_manager.override(mock_script_manager)
        
        # 测试Redis缓存装饰器创建
        with patch.object(RedisCacheDecorator, '__init__', return_value=None) as mock_init:
            cache_decorator = container.redis_cache_decorator()
            
            # 验证RedisCacheDecorator被正确初始化
            mock_init.assert_called_once()

    @pytest.mark.unit
    def test_redis_token_bucket_decorator_creation(self):
        """测试Redis令牌桶装饰器创建"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis脚本管理器
        mock_script_manager = MagicMock()
        container.redis_script_manager.override(mock_script_manager)
        
        # 测试Redis令牌桶装饰器创建
        with patch.object(TokenBucketDecorator, '__init__', return_value=None) as mock_init:
            token_bucket_decorator = container.redis_token_bucket_decorator()
            
            # 验证TokenBucketDecorator被正确初始化
            mock_init.assert_called_once()

    @pytest.mark.unit
    def test_session_provider_creation(self):
        """测试会话提供者创建"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock数据库客户端
        mock_mysql_client = AsyncMock()
        mock_postgres_client = AsyncMock()
        
        container.mysql_client.override(mock_mysql_client)
        container.postgres_client.override(mock_postgres_client)
        
        # 测试会话提供者
        with patch('commonlib.storages.persistence.mysql.client.provide_mysql_session') as mock_mysql_session, \
             patch('commonlib.storages.persistence.postgres.provide_postgres_session') as mock_postgres_session:
            
            mysql_session = container.mysql_session_provider()
            postgres_session = container.postgres_session_provider()
            
            # 验证会话提供者被调用
            mock_mysql_session.assert_called_once_with(mock_mysql_client)
            mock_postgres_session.assert_called_once_with(mock_postgres_client)

    @pytest.mark.unit
    def test_container_dependency_injection(self):
        """测试容器依赖注入"""
        # Mock配置容器
        mock_config = MagicMock()
        mock_config.persistence_config = MagicMock()
        mock_config.connection_priority_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # 验证配置依赖正确注入
        assert container.config is mock_config
        
        # 验证connector_registry依赖配置
        connector_registry_provider = container.connector_registry
        assert isinstance(connector_registry_provider, providers.Callable)
        
        # 验证connection_manager依赖配置和connector_registry
        connection_manager_provider = container.connection_manager
        assert isinstance(connection_manager_provider, providers.Singleton)
