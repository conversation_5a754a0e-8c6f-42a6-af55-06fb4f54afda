"""
ConfigContainer单元测试

测试配置容器的各项功能
"""

import pytest
from unittest.mock import patch, MagicMock, call
from pathlib import Path

from dependency_injector import providers
from commonlib.core.containers.config_container import ConfigContainer, set_up_config_di
from commonlib.configs.base_setting import AppSettings
from commonlib.configs.config_loader import ConfigLoader


class TestConfigContainer:
    """ConfigContainer测试类"""

    @pytest.mark.unit
    def test_container_initialization(self):
        """测试容器初始化"""
        container = ConfigContainer()
        
        # 验证容器是声明式容器
        assert hasattr(container, 'config_loader')
        assert hasattr(container, 'config')
        assert hasattr(container, 'logger')
        assert hasattr(container, 'persistence_config')
        assert hasattr(container, 'connection_priority_config')
        assert hasattr(container, 'redis_config')
        assert hasattr(container, 'mysql_config')

    @pytest.mark.unit
    def test_config_loader_is_singleton(self):
        """测试配置加载器是单例"""
        container = ConfigContainer()
        
        # 验证config_loader是Singleton provider
        assert isinstance(container.config_loader, providers.Singleton)
        
        # 验证多次获取返回同一实例
        loader1 = container.config_loader()
        loader2 = container.config_loader()
        assert loader1 is loader2

    @pytest.mark.unit
    def test_config_is_resource(self):
        """测试配置是资源provider"""
        container = ConfigContainer()
        
        # 验证config是Resource provider
        assert isinstance(container.config, providers.Resource)

    @pytest.mark.unit
    def test_logger_is_resource(self):
        """测试日志是资源provider"""
        container = ConfigContainer()
        
        # 验证logger是Resource provider
        assert isinstance(container.logger, providers.Resource)

    @pytest.mark.unit
    def test_config_providers_are_callable(self):
        """测试配置提供者是Callable"""
        container = ConfigContainer()
        
        # 验证各种配置提供者都是Callable
        assert isinstance(container.persistence_config, providers.Callable)
        assert isinstance(container.connection_priority_config, providers.Callable)
        assert isinstance(container.redis_config, providers.Callable)
        assert isinstance(container.mysql_config, providers.Callable)

    @pytest.mark.unit
    @patch('commonlib.core.containers.config_container.load_dotenv')
    @patch('commonlib.core.logging.tsif_logging.app_logger.initialize')
    def test_container_with_mocked_dependencies(self, mock_logger_init, mock_load_dotenv):
        """测试带Mock依赖的容器"""
        container = ConfigContainer()
        
        # Mock配置加载器
        mock_loader = MagicMock(spec=ConfigLoader)
        mock_config = MagicMock()
        mock_config.application.project_name = "test_app"
        mock_config.debug = True
        mock_config.log_dir = "/tmp/test_logs"
        mock_config.persistence = MagicMock()
        mock_config.connection_priority = MagicMock()
        
        mock_loader.get_config.return_value = mock_config
        container.config_loader.override(mock_loader)
        
        # 初始化资源
        container.init_resources()
        
        # 验证配置可以正确获取
        config = container.config()
        assert config.application.project_name == "test_app"
        assert config.debug is True

    @pytest.mark.unit
    @patch('commonlib.core.containers.config_container.load_dotenv')
    def test_container_provider_dependencies(self, mock_load_dotenv):
        """测试容器provider依赖关系"""
        container = ConfigContainer()
        
        # Mock配置加载器和配置
        mock_loader = MagicMock()
        mock_config = MagicMock()
        mock_config.persistence = MagicMock()
        mock_config.connection_priority = MagicMock()
        mock_config.persistence.redis = MagicMock()
        mock_config.persistence.mysql = MagicMock()
        
        mock_loader.get_config.return_value = mock_config
        container.config_loader.override(mock_loader)
        
        # 测试各种配置提供者
        persistence_config = container.persistence_config()
        assert persistence_config is mock_config.persistence
        
        connection_priority_config = container.connection_priority_config()
        assert connection_priority_config is mock_config.connection_priority
        
        redis_config = container.redis_config()
        assert redis_config is mock_config.persistence.redis
        
        mysql_config = container.mysql_config()
        assert mysql_config is mock_config.persistence.mysql


class TestSetUpConfigDI:
    """set_up_config_di函数测试类"""

    @pytest.mark.unit
    @patch('commonlib.core.containers.config_container.load_dotenv')
    @patch('commonlib.core.logging.tsif_logging.app_logger.initialize')
    def test_setup_config_di_default(self, mock_logger_init, mock_load_dotenv):
        """测试默认配置DI设置"""
        with patch.object(ConfigContainer, 'init_resources') as mock_init_resources:
            container = set_up_config_di()
            
            # 验证返回ConfigContainer实例
            assert isinstance(container, ConfigContainer)
            
            # 验证init_resources被调用
            mock_init_resources.assert_called_once()

    @pytest.mark.unit
    @patch('commonlib.core.containers.config_container.load_dotenv')
    @patch('commonlib.core.logging.tsif_logging.app_logger.initialize')
    def test_setup_config_di_with_app_settings(self, mock_logger_init, mock_load_dotenv):
        """测试带应用设置的配置DI设置"""
        app_settings = AppSettings(project_name="test_app", debug=True)
        
        with patch.object(ConfigContainer, 'init_resources') as mock_init_resources, \
             patch.object(ConfigLoader, 'load') as mock_load:
            
            container = set_up_config_di(app_setting=app_settings)
            
            # 验证load方法被调用
            mock_load.assert_called_once_with(app_settings, None, None)
            
            # 验证返回ConfigContainer实例
            assert isinstance(container, ConfigContainer)

    @pytest.mark.unit
    @patch('commonlib.core.containers.config_container.load_dotenv')
    @patch('commonlib.core.logging.tsif_logging.app_logger.initialize')
    def test_setup_config_di_with_config_path(self, mock_logger_init, mock_load_dotenv):
        """测试带配置路径的配置DI设置"""
        config_path = "/test/config.json"
        additional_paths = ["/test/additional.json"]
        
        with patch.object(ConfigContainer, 'init_resources') as mock_init_resources, \
             patch.object(ConfigLoader, 'load') as mock_load:
            
            container = set_up_config_di(
                config_path=config_path,
                additional_paths=additional_paths
            )
            
            # 验证load方法被正确调用
            mock_load.assert_called_once()
            call_args = mock_load.call_args[0]
            assert call_args[1] == config_path
            assert call_args[2] == additional_paths

    @pytest.mark.unit
    @patch('commonlib.core.containers.config_container.load_dotenv')
    @patch('commonlib.core.logging.tsif_logging.app_logger.initialize')
    def test_setup_config_di_resource_initialization_error(self, mock_logger_init, mock_load_dotenv):
        """测试资源初始化错误处理"""
        mock_logger_init.side_effect = Exception("Logger initialization failed")
        
        with patch.object(ConfigContainer, 'init_resources') as mock_init_resources:
            mock_init_resources.side_effect = Exception("Resource initialization failed")
            
            with pytest.raises(Exception, match="Resource initialization failed"):
                set_up_config_di()

    @pytest.mark.unit
    @patch('commonlib.core.containers.config_container.load_dotenv')
    def test_container_dotenv_loading(self, mock_load_dotenv):
        """测试dotenv加载"""
        ConfigContainer()
        
        # 验证load_dotenv被调用
        mock_load_dotenv.assert_called_once()

    @pytest.mark.unit
    @patch('commonlib.core.containers.config_container.load_dotenv')
    @patch('commonlib.core.logging.tsif_logging.app_logger.initialize')
    def test_container_resource_lifecycle(self, mock_logger_init, mock_load_dotenv):
        """测试容器资源生命周期"""
        container = ConfigContainer()
        
        # Mock配置
        mock_loader = MagicMock()
        mock_config = MagicMock()
        mock_config.application.project_name = "test_app"
        mock_config.debug = True
        mock_config.log_dir = "/tmp/test_logs"
        
        mock_loader.get_config.return_value = mock_config
        container.config_loader.override(mock_loader)
        
        # 测试资源初始化
        container.init_resources()
        
        # 验证日志初始化被调用
        mock_logger_init.assert_called_once()
        
        # 测试资源关闭
        container.shutdown_resources()
        
        # 验证资源正确关闭（这里主要是验证没有异常）
        assert True
