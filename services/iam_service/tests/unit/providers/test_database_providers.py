"""
数据库Provider测试

测试数据库相关的providers
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from dependency_injector import providers
from commonlib.core.containers.infra_container import InfraContainer
from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.storages.persistence.mongodb.repository import MongoRepository


class TestDatabaseProviders:
    """数据库Provider测试类"""

    @pytest.mark.unit
    def test_redis_client_provider(self):
        """测试Redis客户端provider"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock连接管理器
        mock_connection_manager = MagicMock()
        mock_redis_connector = AsyncMock()
        mock_connection_manager.get_connector.return_value = mock_redis_connector
        
        container.connection_manager.override(mock_connection_manager)
        
        # 测试Redis客户端获取
        redis_client = container.redis_client()
        
        # 验证连接管理器被正确调用
        mock_connection_manager.get_connector.assert_called_once_with("redis")
        assert redis_client is mock_redis_connector

    @pytest.mark.unit
    def test_mysql_client_provider(self):
        """测试MySQL客户端provider"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock连接管理器
        mock_connection_manager = MagicMock()
        mock_mysql_connector = AsyncMock()
        mock_connection_manager.get_connector.return_value = mock_mysql_connector
        
        container.connection_manager.override(mock_connection_manager)
        
        # 测试MySQL客户端获取
        mysql_client = container.mysql_client()
        
        # 验证连接管理器被正确调用
        mock_connection_manager.get_connector.assert_called_once_with("mysql")
        assert mysql_client is mock_mysql_connector

    @pytest.mark.unit
    def test_postgres_client_provider(self):
        """测试PostgreSQL客户端provider"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock连接管理器
        mock_connection_manager = MagicMock()
        mock_postgres_connector = AsyncMock()
        mock_connection_manager.get_connector.return_value = mock_postgres_connector
        
        container.connection_manager.override(mock_connection_manager)
        
        # 测试PostgreSQL客户端获取
        postgres_client = container.postgres_client()
        
        # 验证连接管理器被正确调用
        mock_connection_manager.get_connector.assert_called_once_with("postgres")
        assert postgres_client is mock_postgres_connector

    @pytest.mark.unit
    def test_mongodb_client_provider(self):
        """测试MongoDB客户端provider"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock连接管理器
        mock_connection_manager = MagicMock()
        mock_mongodb_connector = AsyncMock()
        mock_connection_manager.get_connector.return_value = mock_mongodb_connector
        
        container.connection_manager.override(mock_connection_manager)
        
        # 测试MongoDB客户端获取
        mongodb_client = container.mongodb_client()
        
        # 验证连接管理器被正确调用
        mock_connection_manager.get_connector.assert_called_once_with("mongodb")
        assert mongodb_client is mock_mongodb_connector

    @pytest.mark.unit
    def test_rabbitmq_client_provider(self):
        """测试RabbitMQ客户端provider"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock连接管理器
        mock_connection_manager = MagicMock()
        mock_rabbitmq_connector = AsyncMock()
        mock_connection_manager.get_connector.return_value = mock_rabbitmq_connector
        
        container.connection_manager.override(mock_connection_manager)
        
        # 测试RabbitMQ客户端获取
        rabbitmq_client = container.rabbitmq_client()
        
        # 验证连接管理器被正确调用
        mock_connection_manager.get_connector.assert_called_once_with("rabbitmq")
        assert rabbitmq_client is mock_rabbitmq_connector

    @pytest.mark.unit
    def test_redis_repository_provider(self):
        """测试Redis仓库provider"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis客户端
        mock_redis_client = AsyncMock()
        container.redis_client.override(mock_redis_client)
        
        # 测试Redis仓库创建
        with patch.object(RedisRepository, '__init__', return_value=None) as mock_init:
            redis_repo = container.decorator_redis_repo()
            
            # 验证RedisRepository被正确初始化
            mock_init.assert_called_once_with(
                key_prefix="test_app",
                redis_connector=mock_redis_client
            )

    @pytest.mark.unit
    def test_mongodb_repository_provider(self):
        """测试MongoDB仓库provider"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock MongoDB客户端
        mock_mongodb_client = AsyncMock()
        container.mongodb_client.override(mock_mongodb_client)
        
        # 测试MongoDB仓库创建
        with patch.object(MongoRepository, '__init__', return_value=None) as mock_init:
            mongodb_repo = container.mongodb_repo_factory()
            
            # 验证MongoRepository被正确初始化
            mock_init.assert_called_once_with(client=mock_mongodb_client)

    @pytest.mark.unit
    def test_mysql_session_provider(self):
        """测试MySQL会话provider"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock MySQL客户端
        mock_mysql_client = AsyncMock()
        container.mysql_client.override(mock_mysql_client)
        
        # 测试MySQL会话提供者
        with patch('commonlib.storages.persistence.mysql.client.provide_mysql_session') as mock_provide:
            mock_provide.return_value = AsyncMock()
            
            session_provider = container.mysql_session_provider()
            
            # 验证provide_mysql_session被调用
            mock_provide.assert_called_once_with(mock_mysql_client)

    @pytest.mark.unit
    def test_postgres_session_provider(self):
        """测试PostgreSQL会话provider"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock PostgreSQL客户端
        mock_postgres_client = AsyncMock()
        container.postgres_client.override(mock_postgres_client)
        
        # 测试PostgreSQL会话提供者
        with patch('commonlib.storages.persistence.postgres.provide_postgres_session') as mock_provide:
            mock_provide.return_value = AsyncMock()
            
            session_provider = container.postgres_session_provider()
            
            # 验证provide_postgres_session被调用
            mock_provide.assert_called_once_with(mock_postgres_client)

    @pytest.mark.unit
    def test_client_provider_error_handling(self):
        """测试客户端provider错误处理"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock连接管理器抛出异常
        mock_connection_manager = MagicMock()
        mock_connection_manager.get_connector.side_effect = Exception("Connection failed")
        
        container.connection_manager.override(mock_connection_manager)
        
        # 测试异常处理
        with pytest.raises(Exception, match="Connection failed"):
            container.redis_client()

    @pytest.mark.unit
    def test_repository_provider_with_none_client(self):
        """测试仓库provider处理None客户端"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock Redis客户端返回None
        container.redis_client.override(None)
        
        # 测试Redis仓库创建
        with patch.object(RedisRepository, '__init__', return_value=None) as mock_init:
            redis_repo = container.decorator_redis_repo()
            
            # 验证RedisRepository仍然被初始化，但使用None客户端
            mock_init.assert_called_once_with(
                key_prefix="test_app",
                redis_connector=None
            )

    @pytest.mark.unit
    def test_provider_type_validation(self):
        """测试provider类型验证"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 验证各种provider的类型
        assert isinstance(container.redis_client, providers.Callable)
        assert isinstance(container.mysql_client, providers.Callable)
        assert isinstance(container.postgres_client, providers.Callable)
        assert isinstance(container.mongodb_client, providers.Callable)
        assert isinstance(container.rabbitmq_client, providers.Callable)
        
        assert isinstance(container.decorator_redis_repo, providers.Factory)
        assert isinstance(container.mongodb_repo_factory, providers.Factory)
        
        assert isinstance(container.mysql_session_provider, providers.Callable)
        assert isinstance(container.postgres_session_provider, providers.Callable)

    @pytest.mark.unit
    def test_provider_dependency_chain(self):
        """测试provider依赖链"""
        mock_config = MagicMock()
        mock_config.app_name.provider = "test_app"
        
        container = InfraContainer(config=mock_config)
        
        # Mock连接管理器
        mock_connection_manager = MagicMock()
        mock_redis_connector = AsyncMock()
        mock_connection_manager.get_connector.return_value = mock_redis_connector
        
        container.connection_manager.override(mock_connection_manager)
        
        # 测试依赖链：connection_manager -> redis_client -> redis_repo
        with patch.object(RedisRepository, '__init__', return_value=None) as mock_init:
            redis_repo = container.decorator_redis_repo()
            
            # 验证整个依赖链正确工作
            mock_connection_manager.get_connector.assert_called_once_with("redis")
            mock_init.assert_called_once_with(
                key_prefix="test_app",
                redis_connector=mock_redis_connector
            )
