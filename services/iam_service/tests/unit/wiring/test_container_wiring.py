"""
Container Wiring测试

测试容器的wiring机制
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch

from dependency_injector.wiring import inject, Provide
from commonlib.core.containers.infra_container import InfraContainer
from container import ServiceContainer


class TestContainerWiring:
    """Container Wiring测试类"""

    @pytest.mark.unit
    def test_infra_container_wiring(self):
        """测试基础设施容器wiring"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 测试模块列表
        test_modules = ["test_module1", "test_module2"]
        
        # Mock wire方法
        with patch.object(container, 'wire') as mock_wire:
            container.wire(modules=test_modules)
            
            # 验证wire方法被正确调用
            mock_wire.assert_called_once_with(modules=test_modules)

    @pytest.mark.unit
    def test_service_container_wiring(self):
        """测试服务容器wiring"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 测试模块列表
        test_modules = ["services.tenant_service", "services.user_service"]
        
        # Mock wire方法
        with patch.object(container, 'wire') as mock_wire:
            container.wire(modules=test_modules)
            
            # 验证wire方法被正确调用
            mock_wire.assert_called_once_with(modules=test_modules)

    @pytest.mark.unit
    def test_container_unwiring(self):
        """测试容器unwiring"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock unwire方法
        with patch.object(container, 'unwire') as mock_unwire:
            container.unwire()
            
            # 验证unwire方法被调用
            mock_unwire.assert_called_once()

    @pytest.mark.unit
    def test_wiring_with_specific_modules(self):
        """测试特定模块的wiring"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container = ServiceContainer(config=mock_config, infra=mock_infra)
        
        # 定义要wire的模块
        wire_modules = [
            "routes.tenants",
            "routes.users", 
            "routes.auth",
            "services.tenant_service",
            "services.user_service",
            "services.auth_service"
        ]
        
        # Mock wire方法
        with patch.object(container, 'wire') as mock_wire:
            container.wire(modules=wire_modules)
            
            # 验证wire方法使用正确的模块列表
            mock_wire.assert_called_once_with(modules=wire_modules)

    @pytest.mark.unit
    def test_wiring_error_handling(self):
        """测试wiring错误处理"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock wire方法抛出异常
        with patch.object(container, 'wire') as mock_wire:
            mock_wire.side_effect = Exception("Wiring failed")
            
            # 测试异常处理
            with pytest.raises(Exception, match="Wiring failed"):
                container.wire(modules=["invalid_module"])

    @pytest.mark.unit
    def test_multiple_container_wiring(self):
        """测试多容器wiring"""
        # 创建多个容器
        mock_config = MagicMock()
        infra_container = InfraContainer(config=mock_config)
        service_container = ServiceContainer(config=mock_config, infra=infra_container)
        
        # 定义不同的模块列表
        infra_modules = ["domain_common.interface.infra_redis.rd_decorator"]
        service_modules = ["services.tenant_service", "services.user_service"]
        
        # Mock wire方法
        with patch.object(infra_container, 'wire') as mock_infra_wire, \
             patch.object(service_container, 'wire') as mock_service_wire:
            
            # Wire不同的容器
            infra_container.wire(modules=infra_modules)
            service_container.wire(modules=service_modules)
            
            # 验证每个容器都被正确wire
            mock_infra_wire.assert_called_once_with(modules=infra_modules)
            mock_service_wire.assert_called_once_with(modules=service_modules)

    @pytest.mark.unit
    def test_wiring_scope_isolation(self):
        """测试wiring范围隔离"""
        mock_config = MagicMock()
        mock_infra = MagicMock()
        
        container1 = ServiceContainer(config=mock_config, infra=mock_infra)
        container2 = ServiceContainer(config=mock_config, infra=mock_infra)
        
        modules1 = ["module1", "module2"]
        modules2 = ["module3", "module4"]
        
        # Mock wire方法
        with patch.object(container1, 'wire') as mock_wire1, \
             patch.object(container2, 'wire') as mock_wire2:
            
            # Wire不同的容器到不同的模块
            container1.wire(modules=modules1)
            container2.wire(modules=modules2)
            
            # 验证每个容器只wire自己的模块
            mock_wire1.assert_called_once_with(modules=modules1)
            mock_wire2.assert_called_once_with(modules=modules2)

    @pytest.mark.unit
    def test_wiring_with_empty_modules(self):
        """测试空模块列表的wiring"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock wire方法
        with patch.object(container, 'wire') as mock_wire:
            container.wire(modules=[])
            
            # 验证wire方法被调用，即使模块列表为空
            mock_wire.assert_called_once_with(modules=[])

    @pytest.mark.unit
    def test_wiring_with_none_modules(self):
        """测试None模块的wiring"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock wire方法
        with patch.object(container, 'wire') as mock_wire:
            container.wire(modules=None)
            
            # 验证wire方法被调用
            mock_wire.assert_called_once_with(modules=None)

    @pytest.mark.unit
    def test_container_wiring_state(self):
        """测试容器wiring状态"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 初始状态应该是未wired
        assert not hasattr(container, '_wired_modules') or not container._wired_modules
        
        # Mock wire和unwire方法
        with patch.object(container, 'wire') as mock_wire, \
             patch.object(container, 'unwire') as mock_unwire:
            
            # Wire容器
            test_modules = ["test_module"]
            container.wire(modules=test_modules)
            mock_wire.assert_called_once_with(modules=test_modules)
            
            # Unwire容器
            container.unwire()
            mock_unwire.assert_called_once()

    @pytest.mark.unit
    def test_wiring_with_duplicate_modules(self):
        """测试重复模块的wiring"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # 包含重复模块的列表
        modules_with_duplicates = [
            "module1", 
            "module2", 
            "module1",  # 重复
            "module3"
        ]
        
        # Mock wire方法
        with patch.object(container, 'wire') as mock_wire:
            container.wire(modules=modules_with_duplicates)
            
            # 验证wire方法被调用，包含重复模块
            mock_wire.assert_called_once_with(modules=modules_with_duplicates)

    @pytest.mark.unit
    def test_partial_wiring_failure(self):
        """测试部分wiring失败"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock wire方法部分失败
        def side_effect(*args, **kwargs):
            modules = kwargs.get('modules', [])
            if 'failing_module' in modules:
                raise Exception("Module wiring failed")
            return None
        
        with patch.object(container, 'wire') as mock_wire:
            mock_wire.side_effect = side_effect
            
            # 测试包含失败模块的wiring
            with pytest.raises(Exception, match="Module wiring failed"):
                container.wire(modules=["good_module", "failing_module"])

    @pytest.mark.unit
    def test_wiring_cleanup_on_error(self):
        """测试错误时的wiring清理"""
        mock_config = MagicMock()
        container = InfraContainer(config=mock_config)
        
        # Mock wire和unwire方法
        with patch.object(container, 'wire') as mock_wire, \
             patch.object(container, 'unwire') as mock_unwire:
            
            # Wire方法抛出异常
            mock_wire.side_effect = Exception("Wiring failed")
            
            # 测试异常处理和清理
            with pytest.raises(Exception, match="Wiring failed"):
                container.wire(modules=["test_module"])
            
            # 在实际实现中，可能需要在异常时调用unwire进行清理
            # 这里只是验证wire方法被调用
            mock_wire.assert_called_once()
