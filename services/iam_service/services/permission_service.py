"""
权限服务

提供权限管理的业务逻辑实现
支持权限的CRUD操作、权限树管理、权限检查等功能
"""

import uuid
import base64
import json
from datetime import datetime
from typing import Dict, Any, Optional, List, Type

from sqlalchemy import select, func, and_, or_, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.exceptions.exceptions import (
    ValidationError, DuplicateResourceError, NotFoundError,
    DatabaseError, BusinessError
)
from domain_common.models import CommonStatus
from domain_common.models.iam_models import (
    User, Tenant, Role, Permission,
    UserRole, RolePermission, UserPolicy,
    PermissionPolicy,
    AuditLog, AuditLogBuilder
)


class PermissionService:
    """权限服务类"""

    def __init__(
        self,
        session: AsyncSession,
        redis_repo: RedisRepository,
        user_model: Type[User],
        tenant_model: Type[Tenant],
        role_model: Type[Role],
        permission_model: Type[Permission],
        user_role_model: Type[UserRole],
        role_permission_model: Type[RolePermission],
        user_policy_model: Type[UserPolicy],
        permission_policy_model: Type[PermissionPolicy],
        audit_log_model: Type[AuditLog]
    ):
        # 数据库会话和缓存
        self.session = session
        self.redis_repo = redis_repo

        # 核心业务模型
        self.user_model = user_model
        self.tenant_model = tenant_model
        self.role_model = role_model
        self.permission_model = permission_model

        # 关联关系模型
        self.user_role_model = user_role_model
        self.role_permission_model = role_permission_model
        self.user_policy_model = user_policy_model

        # 策略模型
        self.permission_policy_model = permission_policy_model

        # 审计模型
        self.audit_log_model = audit_log_model

        # 缓存键前缀
        self.permission_cache_prefix = "permission_info:"
        self.permission_tree_cache_prefix = "permission_tree:"
        self.user_permissions_cache_prefix = "user_permissions:"

    async def create_permission(
        self,
        permission_name: str,
        permission_code: str,
        description: Optional[str],
        resource_type: str,
        action: str,
        tenant_id: str,
        parent_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """创建权限"""
        try:
            # 1. 验证租户存在性
            tenant = await self._get_tenant_by_id(tenant_id)
            if not tenant or tenant.status != CommonStatus.ACTIVE:
                raise NotFoundError("租户不存在或未激活")

            # 2. 检查权限编码唯一性
            await self._validate_permission_code_uniqueness(tenant_id, permission_code)

            # 3. 验证父权限存在性和层级关系
            level = 1
            if parent_id:
                parent_permission = await self._get_permission_by_id(parent_id)
                if not parent_permission or parent_permission.tenant_id != tenant_id:
                    raise NotFoundError("父权限不存在")
                level = parent_permission.level + 1
                if level > 5:  # 限制权限层级深度
                    raise ValidationError("权限层级过深，最多支持5级")

            # 4. 生成权限ID
            permission_id = f"perm_{uuid.uuid4()}"

            # 5. 创建权限记录
            permission = self.permission_model(
                permission_id=permission_id,
                tenant_id=tenant_id,
                permission_name=permission_name,
                permission_code=permission_code,
                description=description,
                resource=resource_type,
                action=action,
                level=level,
                parent_permission_id=parent_id,
                is_inheritable=True,
                status=CommonStatus.ACTIVE,
                meta_data={},
                created_at=datetime.utcnow()
            )
            self.session.add(permission)
            await self.session.flush()

            # 6. 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="CREATE_PERMISSION",
                resource_type="PERMISSION",
                resource_id=permission_id,
                details={
                    "permission_name": permission_name,
                    "permission_code": permission_code,
                    "resource_type": resource_type,
                    "action": action,
                    "level": level
                }
            )

            await self.session.commit()

            # 7. 清理权限树缓存
            await self._clear_permission_tree_cache(tenant_id)

            return {
                "permission_id": permission_id,
                "permission_name": permission_name,
                "permission_code": permission_code,
                "description": description,
                "resource_type": resource_type,
                "action": action,
                "status": CommonStatus.ACTIVE,
                "tenant_id": tenant_id,
                "parent_id": parent_id,
                "level": level,
                "children": [],
                "created_at": permission.created_at.isoformat(),
                "updated_at": permission.updated_at.isoformat() if permission.updated_at else None
            }

        except IntegrityError as e:
            await self.session.rollback()
            if "permission_code" in str(e):
                raise DuplicateResourceError("权限代码已存在")
            else:
                raise DatabaseError("数据库约束错误")
        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"创建权限失败: {str(e)}")
    
    async def list_permissions(
        self,
        tenant_id: str,
        cursor: Optional[str] = None,
        limit: int = 20,
        search: Optional[str] = None,
        resource_type: Optional[str] = None,
        parent_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取权限列表"""
        try:
            # 构建查询条件
            conditions = [
                self.permission_model.tenant_id == tenant_id,
                self.permission_model.status == CommonStatus.ACTIVE
            ]

            # 搜索条件
            if search:
                search_condition = or_(
                    self.permission_model.permission_name.ilike(f"%{search}%"),
                    self.permission_model.permission_code.ilike(f"%{search}%"),
                    self.permission_model.description.ilike(f"%{search}%")
                )
                conditions.append(search_condition)

            # 资源类型过滤
            if resource_type:
                conditions.append(self.permission_model.resource == resource_type)

            # 父权限过滤
            if parent_id:
                conditions.append(self.permission_model.parent_permission_id == parent_id)

            # 游标分页处理
            if cursor:
                try:
                    cursor_data = json.loads(base64.b64decode(cursor).decode())
                    cursor_id = cursor_data.get("id")
                    if cursor_id:
                        conditions.append(self.permission_model.permission_id > cursor_id)
                except Exception:
                    # 游标解析失败，忽略游标
                    pass

            # 查询总数
            count_stmt = select(func.count(self.permission_model.permission_id)).where(and_(*conditions))
            total_result = await self.session.execute(count_stmt)
            total = total_result.scalar()

            # 分页查询
            stmt = (
                select(self.permission_model)
                .where(and_(*conditions))
                .order_by(self.permission_model.level, self.permission_model.created_at)
                .limit(limit + 1)  # 多查一条用于判断是否有下一页
            )
            result = await self.session.execute(stmt)
            permissions = result.scalars().all()

            # 判断是否有更多数据
            has_more = len(permissions) > limit
            if has_more:
                permissions = permissions[:-1]  # 移除多查的一条

            # 构建权限列表
            permission_list = []
            for permission in permissions:
                permission_info = {
                    "permission_id": permission.permission_id,
                    "permission_name": permission.permission_name,
                    "permission_code": permission.permission_code,
                    "description": permission.description,
                    "resource_type": permission.resource,
                    "action": permission.action,
                    "status": permission.status,
                    "tenant_id": permission.tenant_id,
                    "parent_id": permission.parent_permission_id,
                    "level": permission.level,
                    "children": [],  # 列表查询不包含子权限
                    "created_at": permission.created_at.isoformat(),
                    "updated_at": permission.updated_at.isoformat() if permission.updated_at else None
                }
                permission_list.append(permission_info)

            # 生成下一页游标
            next_cursor = None
            if has_more and permissions:
                last_permission = permissions[-1]
                cursor_data = {"id": last_permission.permission_id}
                next_cursor = base64.b64encode(json.dumps(cursor_data).encode()).decode()

            return {
                "permissions": permission_list,
                "total": total,
                "next_cursor": next_cursor,
                "has_more": has_more
            }

        except Exception as e:
            raise BusinessError(f"查询权限列表失败: {str(e)}")
    
    async def get_permission_detail(
        self,
        permission_id: str,
        tenant_id: str
    ) -> Dict[str, Any]:
        """获取权限详情"""
        try:
            # 先尝试从缓存获取
            cache_key = f"{self.permission_cache_prefix}{permission_id}"
            cached_permission = await self.redis_repo.get(cache_key)
            if cached_permission:
                return cached_permission

            # 从数据库查询
            permission = await self._get_permission_by_id(permission_id)
            if not permission or permission.tenant_id != tenant_id:
                raise NotFoundError("权限不存在")

            # 查询子权限
            children_stmt = select(self.permission_model).where(
                and_(
                    self.permission_model.parent_permission_id == permission_id,
                    self.permission_model.status == CommonStatus.ACTIVE
                )
            ).order_by(self.permission_model.created_at)
            children_result = await self.session.execute(children_stmt)
            children = children_result.scalars().all()

            # 构建子权限列表
            children_list = []
            for child in children:
                child_info = {
                    "permission_id": child.permission_id,
                    "permission_name": child.permission_name,
                    "permission_code": child.permission_code,
                    "description": child.description,
                    "resource_type": child.resource,
                    "action": child.action,
                    "status": child.status,
                    "tenant_id": child.tenant_id,
                    "parent_id": child.parent_permission_id,
                    "level": child.level,
                    "children": [],  # 详情查询只返回一级子权限
                    "created_at": child.created_at.isoformat(),
                    "updated_at": child.updated_at.isoformat() if child.updated_at else None
                }
                children_list.append(child_info)

            # 构建权限详情
            permission_detail = {
                "permission_id": permission.permission_id,
                "permission_name": permission.permission_name,
                "permission_code": permission.permission_code,
                "description": permission.description,
                "resource_type": permission.resource,
                "action": permission.action,
                "status": permission.status,
                "tenant_id": permission.tenant_id,
                "parent_id": permission.parent_permission_id,
                "level": permission.level,
                "children": children_list,
                "created_at": permission.created_at.isoformat(),
                "updated_at": permission.updated_at.isoformat() if permission.updated_at else None
            }

            # 缓存权限详情
            await self.redis_repo.set(cache_key, permission_detail, ttl=1800)

            return permission_detail

        except Exception as e:
            if isinstance(e, NotFoundError):
                raise
            raise BusinessError(f"查询权限详情失败: {str(e)}")
    
    async def update_permission(
        self,
        permission_id: str,
        tenant_id: str,
        permission_name: Optional[str] = None,
        description: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """更新权限信息"""
        try:
            # 获取权限
            permission = await self._get_permission_by_id(permission_id)
            if not permission or permission.tenant_id != tenant_id:
                raise NotFoundError("权限不存在")

            # 检查系统权限保护
            is_system = False
            if permission.meta_data and isinstance(permission.meta_data, dict):
                is_system = permission.meta_data.get("is_system", False)

            if is_system and status == CommonStatus.INACTIVE:
                raise ValidationError("系统权限不可禁用")

            # 更新权限信息
            updated = False
            if permission_name is not None and permission_name != permission.permission_name:
                permission.permission_name = permission_name
                updated = True

            if description is not None and description != permission.description:
                permission.description = description
                updated = True

            if status is not None and status != permission.status:
                # 验证状态值
                if status not in [CommonStatus.ACTIVE, CommonStatus.INACTIVE]:
                    raise ValidationError("无效的状态值")
                permission.status = status
                updated = True

            if updated:
                permission.updated_at = datetime.utcnow()

                # 记录审计日志
                await self._create_audit_log(
                    tenant_id=tenant_id,
                    user_id=None,
                    action="UPDATE_PERMISSION",
                    resource_type="PERMISSION",
                    resource_id=permission_id,
                    details={
                        "permission_name": permission_name,
                        "description": description,
                        "status": status
                    }
                )

                await self.session.commit()

                # 清除缓存
                await self._clear_permission_cache(permission_id)
                await self._clear_permission_tree_cache(tenant_id)

            # 返回更新后的权限信息
            return await self.get_permission_detail(permission_id, tenant_id)

        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (NotFoundError, ValidationError)):
                raise
            raise BusinessError(f"更新权限失败: {str(e)}")
    
    async def delete_permission(
        self,
        permission_id: str,
        tenant_id: str,
        force: bool = False
    ) -> Dict[str, Any]:
        """删除权限"""
        try:
            # 获取权限
            permission = await self._get_permission_by_id(permission_id)
            if not permission or permission.tenant_id != tenant_id:
                raise NotFoundError("权限不存在")

            # 检查系统权限保护
            is_system = False
            if permission.meta_data and isinstance(permission.meta_data, dict):
                is_system = permission.meta_data.get("is_system", False)

            if is_system:
                raise ValidationError("系统权限不可删除")

            # 检查是否有子权限
            children_count = await self._get_permission_children_count(permission_id)
            if children_count > 0 and not force:
                raise ValidationError("权限有子权限，请先删除子权限或使用强制删除")

            # 检查权限使用情况
            usage_info = await self._check_permission_usage(permission_id)
            if (usage_info["role_count"] > 0 or usage_info["policy_count"] > 0) and not force:
                raise ValidationError("权限正在被使用，请先解除关联或使用强制删除")

            # 执行删除操作
            cleanup_summary = {}

            if force:
                # 强制删除：清理所有关联
                cleanup_summary = await self._force_delete_permission_associations(permission_id)

            # 删除子权限（如果有）
            if children_count > 0:
                await self._delete_permission_children(permission_id)
                cleanup_summary["children_deleted"] = children_count

            # 软删除权限
            permission.status = CommonStatus.DELETED
            permission.updated_at = datetime.utcnow()

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="DELETE_PERMISSION",
                resource_type="PERMISSION",
                resource_id=permission_id,
                details={
                    "permission_name": permission.permission_name,
                    "permission_code": permission.permission_code,
                    "force": force,
                    "cleanup_summary": cleanup_summary
                }
            )

            await self.session.commit()

            # 清除缓存
            await self._clear_permission_cache(permission_id)
            await self._clear_permission_tree_cache(tenant_id)

            return {
                "success": True,
                "permission_id": permission_id
            }

        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (NotFoundError, ValidationError)):
                raise
            raise BusinessError(f"删除权限失败: {str(e)}")
    
    async def check_user_permission(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """检查用户权限"""
        user_id = data.get("user_id")
        permission_code = data.get("permission_code")
        resource = data.get("resource")
        action = data.get("action")
        
        # TODO: 实现权限检查逻辑
        # 1. 获取用户角色
        # 2. 获取角色权限
        # 3. 检查直接权限
        # 4. 应用权限策略
        # 5. 返回检查结果
        
        # 先从缓存获取
        cache_key = f"permission:{user_id}:{permission_code}"
        cached_result = await self.redis_repo.get(cache_key)
        if cached_result:
            return cached_result
        
        result = {
            "user_id": user_id,
            "permission_code": permission_code,
            "resource": resource,
            "action": action,
            "has_permission": True,
            "permission_source": "role",
            "granted_by": "role_admin",
            "checked_at": "2025-01-22 10:30:45"
        }
        
        # 缓存结果
        await self.redis_repo.set(cache_key, result, ttl=1800)
        
        return result
    
    async def get_user_permissions(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """获取用户权限列表"""
        user_id = data.get("user_id")
        
        # TODO: 实现用户权限查询逻辑
        return {
            "user_id": user_id,
            "permissions": [
                {
                    "permission_id": "perm_user_read",
                    "permission_name": "查看用户",
                    "permission_code": "USER_READ",
                    "resource": "user",
                    "action": "read",
                    "source": "role",
                    "granted_by": "role_admin"
                }
            ],
            "total": 1
        }
    
    async def get_role_permissions(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """获取角色权限列表"""
        role_id = data.get("role_id")

        # TODO: 实现角色权限查询逻辑
        return {
            "role_id": role_id,
            "permissions": [
                {
                    "permission_id": "perm_user_read",
                    "permission_name": "查看用户",
                    "permission_code": "USER_READ",
                    "resource": "user",
                    "action": "read",
                    "assigned_at": "2025-01-22 10:30:45"
                }
            ],
            "total": 1
        }

    async def get_permission_tree(
        self,
        tenant_id: str,
        resource_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取权限树"""
        try:
            # 先尝试从缓存获取
            cache_key = f"{self.permission_tree_cache_prefix}{tenant_id}"
            if resource_type:
                cache_key += f":{resource_type}"

            cached_tree = await self.redis_repo.get(cache_key)
            if cached_tree:
                return cached_tree

            # 构建查询条件
            conditions = [
                self.permission_model.tenant_id == tenant_id,
                self.permission_model.status == CommonStatus.ACTIVE
            ]

            if resource_type:
                conditions.append(self.permission_model.resource == resource_type)

            # 查询所有权限
            stmt = (
                select(self.permission_model)
                .where(and_(*conditions))
                .order_by(self.permission_model.level, self.permission_model.created_at)
            )
            result = await self.session.execute(stmt)
            permissions = result.scalars().all()

            # 构建权限映射
            permission_map = {}
            for permission in permissions:
                permission_info = {
                    "permission_id": permission.permission_id,
                    "permission_name": permission.permission_name,
                    "permission_code": permission.permission_code,
                    "description": permission.description,
                    "resource_type": permission.resource,
                    "action": permission.action,
                    "status": permission.status,
                    "tenant_id": permission.tenant_id,
                    "parent_id": permission.parent_permission_id,
                    "level": permission.level,
                    "children": [],
                    "created_at": permission.created_at.isoformat(),
                    "updated_at": permission.updated_at.isoformat() if permission.updated_at else None
                }
                permission_map[permission.permission_id] = permission_info

            # 构建树结构
            tree = []
            for permission_info in permission_map.values():
                if permission_info["parent_id"]:
                    # 有父权限，添加到父权限的children中
                    parent = permission_map.get(permission_info["parent_id"])
                    if parent:
                        parent["children"].append(permission_info)
                else:
                    # 根权限，添加到树的根级
                    tree.append(permission_info)

            # 按名称排序
            tree.sort(key=lambda x: x["permission_name"])
            for permission_info in permission_map.values():
                permission_info["children"].sort(key=lambda x: x["permission_name"])

            result_data = {
                "tree": tree,
                "total": len(permissions)
            }

            # 缓存权限树
            await self.redis_repo.set(cache_key, result_data, ttl=3600)

            return result_data

        except Exception as e:
            raise BusinessError(f"查询权限树失败: {str(e)}")

    # ===== 辅助方法 =====

    async def _get_tenant_by_id(self, tenant_id: str):
        """根据ID获取租户"""
        stmt = select(self.tenant_model).where(self.tenant_model.tenant_id == tenant_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_permission_by_id(self, permission_id: str):
        """根据ID获取权限"""
        stmt = select(self.permission_model).where(self.permission_model.permission_id == permission_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _validate_permission_code_uniqueness(self, tenant_id: str, permission_code: str):
        """验证权限代码唯一性"""
        stmt = select(self.permission_model).where(
            and_(
                self.permission_model.tenant_id == tenant_id,
                self.permission_model.permission_code == permission_code,
                self.permission_model.status != CommonStatus.DELETED
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("权限代码已存在")

    async def _get_permission_children_count(self, permission_id: str) -> int:
        """获取权限子权限数量"""
        stmt = select(func.count(self.permission_model.permission_id)).where(
            and_(
                self.permission_model.parent_permission_id == permission_id,
                self.permission_model.status == CommonStatus.ACTIVE
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar() or 0

    async def _check_permission_usage(self, permission_id: str) -> Dict[str, int]:
        """检查权限使用情况"""
        # 检查角色权限关联
        role_stmt = select(func.count(self.role_permission_model.id)).where(
            and_(
                self.role_permission_model.permission_id == permission_id,
                self.role_permission_model.status == CommonStatus.ACTIVE
            )
        )
        role_result = await self.session.execute(role_stmt)
        role_count = role_result.scalar() or 0

        # TODO: 检查策略关联
        policy_count = 0

        return {
            "role_count": role_count,
            "policy_count": policy_count
        }

    async def _force_delete_permission_associations(self, permission_id: str) -> Dict[str, int]:
        """强制删除权限关联"""
        cleanup_summary = {}

        # 删除角色权限关联
        role_update_stmt = (
            update(self.role_permission_model)
            .where(
                and_(
                    self.role_permission_model.permission_id == permission_id,
                    self.role_permission_model.status == CommonStatus.ACTIVE
                )
            )
            .values(
                status=CommonStatus.INACTIVE,
                updated_at=datetime.utcnow()
            )
        )
        role_result = await self.session.execute(role_update_stmt)
        cleanup_summary["roles_unassigned"] = role_result.rowcount

        # TODO: 删除策略关联
        cleanup_summary["policies_updated"] = 0

        return cleanup_summary

    async def _delete_permission_children(self, permission_id: str):
        """删除权限的所有子权限"""
        update_stmt = (
            update(self.permission_model)
            .where(
                and_(
                    self.permission_model.parent_permission_id == permission_id,
                    self.permission_model.status == CommonStatus.ACTIVE
                )
            )
            .values(
                status=CommonStatus.DELETED,
                updated_at=datetime.utcnow()
            )
        )
        await self.session.execute(update_stmt)

    async def _create_audit_log(self, tenant_id: str, user_id: Optional[str], action: str, resource_type: str, resource_id: str, details: Dict[str, Any]):
        """创建审计日志"""
        try:
            audit_log = AuditLogBuilder.create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                details=details,
                result="success"
            )
            self.session.add(audit_log)
            # 注意：不在这里commit，由调用方决定
        except Exception as e:
            # 审计日志失败不应该影响主业务
            print(f"审计日志记录失败: {action} - {resource_type}:{resource_id} by {user_id} in {tenant_id} - {str(e)}")

    async def _clear_permission_cache(self, permission_id: str):
        """清除权限缓存"""
        await self.redis_repo.delete(f"{self.permission_cache_prefix}{permission_id}")

    async def _clear_permission_tree_cache(self, tenant_id: str):
        """清除权限树缓存"""
        # 清除所有相关的权限树缓存
        pattern = f"{self.permission_tree_cache_prefix}{tenant_id}*"
        keys = await self.redis_repo.keys(pattern)
        if keys:
            await self.redis_repo.delete(*keys)
