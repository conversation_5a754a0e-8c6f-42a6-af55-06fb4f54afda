"""
认证与安全路由

提供登录、登出、密码管理、M<PERSON>、会话管理等安全相关接口
支持多种认证方式、设备管理、敏感操作确认等功能
"""

from fastapi import APIRouter, Depends
from dependency_injector.wiring import inject, Provide
from pydantic import BaseModel, Field, EmailStr
from typing import Optional, List, Dict, Any

from commonlib.schemas.request import BaseRequest
from commonlib.schemas.responses import SuccessResponse, success_response
from container import ServiceContainer
from services.auth_service import AuthService

router = APIRouter()


# ===== 请求模型 =====

class DeviceInfo(BaseModel):
    """设备信息模型"""
    device_id: Optional[str] = Field(
        None,
        description="设备唯一标识符",
        examples=["device_550e8400-e29b-41d4-a716-446655440000"]
    )
    device_name: Optional[str] = Field(
        None,
        description="设备名称",
        examples=["iPhone 15"]
    )
    os: Optional[str] = Field(
        None,
        description="操作系统",
        examples=["iOS 17.0"]
    )
    app_version: Optional[str] = Field(
        None,
        description="应用版本",
        examples=["1.0.0"]
    )
    ip_address: Optional[str] = Field(
        None,
        description="IP地址",
        examples=["*************"]
    )
    user_agent: Optional[str] = Field(
        None,
        description="用户代理字符串",
        examples=["Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)"]
    )


class LoginRequest(BaseModel):
    """
    用户登录请求模型

    支持多种登录方式和安全验证
    """
    tenant_id: str = Field(
        ...,
        description="租户ID，指定登录的租户",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    login_type: str = Field(
        "username",
        description="登录类型：username(用户名)、email(邮箱)、phone(手机号)",
        examples=["phone"]
    )
    identifier: str = Field(
        ...,
        description="登录标识符，根据login_type可以是用户名、邮箱或手机号",
        examples=["13800138000"]
    )
    credential: str = Field(
        ...,
        description="登录凭证，可以是密码或验证码",
        examples=["password123"]
    )
    remember_me: bool = Field(
        False,
        description="是否记住登录状态，影响令牌过期时间",
        examples=[True]
    )
    mfa_code: Optional[str] = Field(
        None,
        description="多因子认证验证码，如果用户启用了MFA则必填",
        examples=["123456"]
    )
    device_info: Optional[DeviceInfo] = Field(
        None,
        description="设备信息，用于设备管理和安全检查"
    )


class LogoutRequest(BaseModel):
    """
    用户登出请求模型

    支持单设备登出和全设备登出
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    session_id: str = Field(
        ...,
        description="会话ID，指定要登出的会话",
        examples=["session_550e8400-e29b-41d4-a716-446655440000"]
    )
    logout_all_devices: bool = Field(
        False,
        description="是否登出所有设备",
        examples=[False]
    )


class RefreshTokenRequest(BaseModel):
    """
    刷新令牌请求模型

    用于刷新访问令牌
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    refresh_token: str = Field(
        ...,
        description="刷新令牌",
        examples=["refresh_token_550e8400-e29b-41d4-a716-446655440000"]
    )
    session_id: str = Field(
        ...,
        description="会话ID",
        examples=["session_550e8400-e29b-41d4-a716-446655440000"]
    )


class ChangePasswordRequest(BaseModel):
    """
    修改密码请求模型

    用户主动修改密码
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    old_password: str = Field(
        ...,
        description="原密码",
        examples=["OldPass123!"]
    )
    new_password: str = Field(
        ...,
        description="新密码",
        examples=["NewPass123!"]
    )
    logout_other_sessions: bool = Field(
        True,
        description="是否登出其他会话",
        examples=[True]
    )


class ForgotPasswordRequest(BaseModel):
    """
    忘记密码请求模型

    发起密码重置流程
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    identifier: str = Field(
        ...,
        description="用户标识符，可以是邮箱或手机号",
        examples=["<EMAIL>"]
    )
    identifier_type: str = Field(
        ...,
        description="标识符类型：email(邮箱) 或 phone(手机号)",
        examples=["email"]
    )
    captcha_token: Optional[str] = Field(
        None,
        description="图形验证码令牌，防止暴力破解",
        examples=["captcha_550e8400-e29b-41d4-a716-446655440000"]
    )


class ResetPasswordRequest(BaseModel):
    """
    重置密码请求模型

    通过重置令牌重置密码
    """
    reset_token: str = Field(
        ...,
        description="密码重置令牌",
        examples=["reset_550e8400-e29b-41d4-a716-446655440000"]
    )
    new_password: str = Field(
        ...,
        description="新密码",
        examples=["NewPass123!"]
    )
    confirm_password: str = Field(
        ...,
        description="确认新密码",
        examples=["NewPass123!"]
    )


class SetupMFARequest(BaseModel):
    """
    设置多因子认证请求模型

    初始化MFA设置
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    mfa_type: str = Field(
        ...,
        description="MFA类型：totp(时间基础一次性密码)、sms(短信)、email(邮箱)",
        examples=["totp"]
    )
    device_name: Optional[str] = Field(
        None,
        description="设备名称，用于标识MFA设备",
        examples=["iPhone 15"]
    )


class VerifyMFARequest(BaseModel):
    """
    验证并启用MFA请求模型

    验证MFA设置并启用
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    setup_token: str = Field(
        ...,
        description="MFA设置令牌",
        examples=["setup_550e8400-e29b-41d4-a716-446655440000"]
    )
    verification_code: str = Field(
        ...,
        description="MFA验证码",
        examples=["123456"]
    )


class DisableMFARequest(BaseModel):
    """
    禁用多因子认证请求模型

    禁用用户的MFA功能
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    password: str = Field(
        ...,
        description="用户当前密码，用于身份验证",
        examples=["UserPass123!"]
    )
    mfa_code: Optional[str] = Field(
        None,
        description="当前MFA验证码，如果用户仍能生成验证码则需要提供",
        examples=["123456"]
    )
    reason: Optional[str] = Field(
        None,
        description="禁用原因，用于审计记录",
        max_length=200,
        examples=["用户更换设备，需要重新设置MFA"]
    )


class ListSessionsRequest(BaseModel):
    """
    查看用户会话请求模型

    获取用户的活跃会话列表
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    include_expired: bool = Field(
        False,
        description="是否包含已过期的会话",
        examples=[False]
    )


class TerminateSessionRequest(BaseModel):
    """
    强制下线会话请求模型

    终止指定的用户会话
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    session_id: str = Field(
        ...,
        description="要终止的会话ID",
        examples=["session_550e8400-e29b-41d4-a716-446655440000"]
    )
    reason: Optional[str] = Field(
        None,
        description="终止原因",
        max_length=200,
        examples=["安全原因"]
    )


class VerifyCodeRequest(BaseModel):
    """
    验证验证码请求模型

    验证各种类型的验证码
    """
    code_id: str = Field(
        ...,
        description="验证码ID",
        examples=["code_550e8400-e29b-41d4-a716-446655440000"]
    )
    verification_code: str = Field(
        ...,
        description="验证码",
        examples=["123456"]
    )
    code_type: str = Field(
        ...,
        description="验证码类型：sms(短信)、email(邮箱)、totp(TOTP)",
        examples=["sms"]
    )


class ConfirmOperationRequest(BaseModel):
    """
    敏感操作确认请求模型

    对敏感操作进行二次确认
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    operation_type: str = Field(
        ...,
        description="操作类型，如：delete_tenant、change_password、disable_mfa等",
        examples=["delete_tenant"]
    )
    operation_data: Dict[str, Any] = Field(
        ...,
        description="操作相关数据",
        examples=[{"tenant_id": "550e8400-e29b-41d4-a716-446655440000"}]
    )
    confirmation_method: str = Field(
        ...,
        description="确认方式：sms(短信)、email(邮箱)、totp(TOTP)、password(密码)",
        examples=["sms"]
    )
    verification_code: str = Field(
        ...,
        description="验证码或密码",
        examples=["123456"]
    )


# ===== 响应数据模型 =====

class UserInfo(BaseModel):
    """用户信息模型"""
    user_id: str = Field(..., description="用户ID", examples=["user_550e8400-e29b-41d4-a716-446655440000"])
    username: str = Field(..., description="用户名", examples=["john_doe"])
    nickname: Optional[str] = Field(None, description="用户昵称", examples=["约翰"])
    email: str = Field(..., description="邮箱地址", examples=["<EMAIL>"])
    phone: Optional[str] = Field(None, description="手机号码", examples=["13800138000"])
    avatar: Optional[str] = Field(None, description="头像URL", examples=["https://domain.com/avatars/user_xxx.jpg"])
    roles: List[str] = Field(default_factory=list, description="用户角色列表", examples=[["USER"]])
    permissions: List[str] = Field(default_factory=list, description="用户权限列表", examples=[["user:read", "document:read"]])
    mfa_enabled: bool = Field(False, description="是否启用多因子认证", examples=[False])
    password_expires_in: Optional[int] = Field(None, description="密码过期天数", examples=[90])


class TenantInfo(BaseModel):
    """租户信息模型"""
    tenant_id: str = Field(..., description="租户ID", examples=["550e8400-e29b-41d4-a716-446655440000"])
    tenant_name: str = Field(..., description="租户名称", examples=["示例企业"])


class SecurityWarning(BaseModel):
    """安全警告模型"""
    type: str = Field(..., description="警告类型", examples=["password_expiring"])
    message: str = Field(..., description="警告消息", examples=["密码将在90天后过期"])
    action_required: bool = Field(False, description="是否需要立即行动", examples=[False])


class LoginResponse(BaseModel):
    """
    登录响应数据模型

    包含令牌信息、用户信息和安全警告
    """
    access_token: str = Field(..., description="访问令牌", examples=["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."])
    refresh_token: str = Field(..., description="刷新令牌", examples=["refresh_token_550e8400-e29b-41d4-a716-446655440000"])
    token_type: str = Field("Bearer", description="令牌类型", examples=["Bearer"])
    expires_in: int = Field(..., description="访问令牌过期时间(秒)", examples=[7200])
    refresh_expires_in: int = Field(..., description="刷新令牌过期时间(秒)", examples=[86400])
    session_id: str = Field(..., description="会话ID", examples=["session_550e8400-e29b-41d4-a716-446655440000"])
    user_info: UserInfo = Field(..., description="用户基本信息")
    tenant_info: TenantInfo = Field(..., description="租户信息")
    security_warnings: List[SecurityWarning] = Field(default_factory=list, description="安全警告列表")


class LogoutResponse(BaseModel):
    """
    登出响应数据模型

    包含登出结果和会话信息
    """
    user_id: str = Field(..., description="用户ID", examples=["user_550e8400-e29b-41d4-a716-446655440000"])
    logout_time: str = Field(..., description="登出时间", examples=["2025-01-15T10:30:45.123456"])
    logged_out_sessions: int = Field(..., description="登出的会话数", examples=[1])
    remaining_sessions: int = Field(..., description="剩余活跃会话数", examples=[2])


class RefreshTokenResponse(BaseModel):
    """
    刷新令牌响应数据模型

    包含新的令牌信息
    """
    access_token: str = Field(..., description="新的访问令牌", examples=["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."])
    refresh_token: str = Field(..., description="新的刷新令牌", examples=["refresh_token_550e8400-e29b-41d4-a716-446655440000"])
    token_type: str = Field("Bearer", description="令牌类型", examples=["Bearer"])
    expires_in: int = Field(..., description="访问令牌过期时间(秒)", examples=[7200])
    refresh_expires_in: int = Field(..., description="刷新令牌过期时间(秒)", examples=[86400])
    issued_at: str = Field(..., description="令牌签发时间", examples=["2025-01-15T10:30:45.123456"])


class ChangePasswordResponse(BaseModel):
    """
    修改密码响应数据模型

    包含密码修改结果
    """
    user_id: str = Field(..., description="用户ID", examples=["user_550e8400-e29b-41d4-a716-446655440000"])
    password_changed_at: str = Field(..., description="密码修改时间", examples=["2025-01-15T10:30:45.123456"])
    password_expires_at: str = Field(..., description="密码过期时间", examples=["2025-04-15T10:30:45.123456"])
    sessions_terminated: int = Field(..., description="终止的会话数", examples=[3])


class ForgotPasswordResponse(BaseModel):
    """
    忘记密码响应数据模型

    包含重置令牌信息
    """
    reset_token_id: str = Field(..., description="重置令牌ID", examples=["reset_550e8400-e29b-41d4-a716-446655440000"])
    expires_in: int = Field(..., description="重置令牌过期时间(秒)", examples=[1800])
    sent_to: str = Field(..., description="发送目标(脱敏)", examples=["j***@demo.com"])


class ResetPasswordResponse(BaseModel):
    """
    重置密码响应数据模型

    包含密码重置结果
    """
    user_id: str = Field(..., description="用户ID", examples=["user_550e8400-e29b-41d4-a716-446655440000"])
    password_reset_at: str = Field(..., description="密码重置时间", examples=["2025-01-15T10:30:45.123456"])
    all_sessions_terminated: bool = Field(..., description="是否终止了所有会话", examples=[True])


class SetupMFAResponse(BaseModel):
    """
    设置MFA响应数据模型

    包含MFA设置信息
    """
    secret_key: str = Field(..., description="TOTP密钥", examples=["JBSWY3DPEHPK3PXP"])
    qr_code_url: str = Field(..., description="二维码URL", examples=["https://domain.com/qr/mfa_xxx.png"])
    backup_codes: List[str] = Field(..., description="备用恢复码", examples=[["123456", "789012", "345678"]])
    setup_token: str = Field(..., description="设置令牌", examples=["setup_550e8400-e29b-41d4-a716-446655440000"])


class VerifyMFAResponse(BaseModel):
    """
    验证MFA响应数据模型

    包含MFA启用结果
    """
    user_id: str = Field(..., description="用户ID", examples=["user_550e8400-e29b-41d4-a716-446655440000"])
    mfa_enabled: bool = Field(..., description="MFA是否已启用", examples=[True])
    enabled_at: str = Field(..., description="启用时间", examples=["2025-01-15T10:30:45.123456"])


class DisableMFAResponse(BaseModel):
    """
    禁用MFA响应数据模型

    包含MFA禁用结果
    """
    user_id: str = Field(..., description="用户ID", examples=["user_550e8400-e29b-41d4-a716-446655440000"])
    mfa_enabled: bool = Field(..., description="MFA是否已禁用", examples=[False])
    disabled_at: str = Field(..., description="禁用时间", examples=["2025-01-15T10:30:45.123456"])
    reason: Optional[str] = Field(None, description="禁用原因", examples=["用户更换设备"])


class SessionDevice(BaseModel):
    """会话设备信息模型"""
    device_name: str = Field(..., description="设备名称", examples=["iPhone 15"])
    os: str = Field(..., description="操作系统", examples=["iOS 17.0"])
    browser: Optional[str] = Field(None, description="浏览器", examples=["Safari"])
    ip_address: str = Field(..., description="IP地址", examples=["*************"])


class SessionInfo(BaseModel):
    """会话信息模型"""
    session_id: str = Field(..., description="会话ID", examples=["session_550e8400-e29b-41d4-a716-446655440000"])
    device_info: SessionDevice = Field(..., description="设备信息")
    created_at: str = Field(..., description="创建时间", examples=["2025-01-15T09:00:00.123456"])
    last_activity: str = Field(..., description="最后活动时间", examples=["2025-01-15T10:30:00.123456"])
    expires_at: str = Field(..., description="过期时间", examples=["2025-01-15T16:00:00.123456"])
    is_current: bool = Field(..., description="是否为当前会话", examples=[True])


class ListSessionsResponse(BaseModel):
    """
    查看用户会话响应数据模型

    包含用户的活跃会话列表
    """
    user_id: str = Field(..., description="用户ID", examples=["user_550e8400-e29b-41d4-a716-446655440000"])
    active_sessions: List[SessionInfo] = Field(..., description="活跃会话列表")
    total_sessions: int = Field(..., description="总会话数", examples=[2])


class TerminateSessionResponse(BaseModel):
    """
    强制下线会话响应数据模型

    包含会话终止结果
    """
    session_id: str = Field(..., description="被终止的会话ID", examples=["session_550e8400-e29b-41d4-a716-446655440000"])
    terminated_at: str = Field(..., description="终止时间", examples=["2025-01-15T10:30:45.123456"])
    reason: Optional[str] = Field(None, description="终止原因", examples=["安全原因"])


class VerifyCodeResponse(BaseModel):
    """
    验证验证码响应数据模型

    包含验证结果
    """
    code_id: str = Field(..., description="验证码ID", examples=["code_550e8400-e29b-41d4-a716-446655440000"])
    verified: bool = Field(..., description="是否验证成功", examples=[True])
    verified_at: str = Field(..., description="验证时间", examples=["2025-01-15T10:30:45.123456"])


class ConfirmOperationResponse(BaseModel):
    """
    敏感操作确认响应数据模型

    包含操作确认结果
    """
    operation_token: str = Field(..., description="操作令牌", examples=["op_token_550e8400-e29b-41d4-a716-446655440000"])
    expires_in: int = Field(..., description="操作令牌过期时间(秒)", examples=[300])
    confirmed_at: str = Field(..., description="确认时间", examples=["2025-01-15T10:30:45.123456"])


# ===== 响应模型包装类 =====

class LoginResponseModel(SuccessResponse[LoginResponse]):
    """登录响应模型"""
    data: LoginResponse = Field(..., description="登录结果数据")


class LogoutResponseModel(SuccessResponse[LogoutResponse]):
    """登出响应模型"""
    data: LogoutResponse = Field(..., description="登出结果数据")


class RefreshTokenResponseModel(SuccessResponse[RefreshTokenResponse]):
    """刷新令牌响应模型"""
    data: RefreshTokenResponse = Field(..., description="刷新令牌结果数据")


class ChangePasswordResponseModel(SuccessResponse[ChangePasswordResponse]):
    """修改密码响应模型"""
    data: ChangePasswordResponse = Field(..., description="修改密码结果数据")


class ForgotPasswordResponseModel(SuccessResponse[ForgotPasswordResponse]):
    """忘记密码响应模型"""
    data: ForgotPasswordResponse = Field(..., description="忘记密码结果数据")


class ResetPasswordResponseModel(SuccessResponse[ResetPasswordResponse]):
    """重置密码响应模型"""
    data: ResetPasswordResponse = Field(..., description="重置密码结果数据")


class SetupMFAResponseModel(SuccessResponse[SetupMFAResponse]):
    """设置MFA响应模型"""
    data: SetupMFAResponse = Field(..., description="设置MFA结果数据")


class VerifyMFAResponseModel(SuccessResponse[VerifyMFAResponse]):
    """验证MFA响应模型"""
    data: VerifyMFAResponse = Field(..., description="验证MFA结果数据")


class DisableMFAResponseModel(SuccessResponse[DisableMFAResponse]):
    """禁用MFA响应模型"""
    data: DisableMFAResponse = Field(..., description="禁用MFA结果数据")


class ListSessionsResponseModel(SuccessResponse[ListSessionsResponse]):
    """查看用户会话响应模型"""
    data: ListSessionsResponse = Field(..., description="用户会话列表数据")


class TerminateSessionResponseModel(SuccessResponse[TerminateSessionResponse]):
    """强制下线会话响应模型"""
    data: TerminateSessionResponse = Field(..., description="强制下线会话结果数据")


class VerifyCodeResponseModel(SuccessResponse[VerifyCodeResponse]):
    """验证验证码响应模型"""
    data: VerifyCodeResponse = Field(..., description="验证验证码结果数据")


class ConfirmOperationResponseModel(SuccessResponse[ConfirmOperationResponse]):
    """敏感操作确认响应模型"""
    data: ConfirmOperationResponse = Field(..., description="敏感操作确认结果数据")


# ===== 路由端点 =====

@router.post(
    "/login",
    summary="用户登录",
    description="""
    用户登录认证

    **功能说明：**
    - 支持多种登录方式（用户名、邮箱、手机号）
    - 支持密码和验证码登录
    - 自动设备识别和安全检查
    - 支持多因子认证（MFA）
    - 生成JWT访问令牌和刷新令牌
    - 创建用户会话并记录设备信息

    **业务规则：**
    - 验证用户身份和账户状态
    - 检查登录频率限制
    - 验证设备安全性
    - 如果启用MFA则需要提供验证码

    **返回数据：**
    - JWT令牌对（访问令牌+刷新令牌）
    - 用户基本信息和权限
    - 租户信息
    - 安全警告（如密码即将过期）
    """,
    response_model=LoginResponseModel,
    responses={
        200: {"description": "登录成功"},
        400: {"description": "请求参数错误"},
        401: {"description": "认证失败"},
        403: {"description": "账户被锁定或禁用"},
        429: {"description": "登录频率过高"},
        500: {"description": "服务器内部错误"}
    },
    tags=["认证与安全"]
)
@inject
async def login(
    request: BaseRequest[LoginRequest],
    auth_service: AuthService = Depends(Provide[ServiceContainer.auth_service])
):
    """
    用户登录

    支持多种登录方式和安全验证的用户认证
    """
    login_params = request.data
    result = await auth_service.login(
        tenant_id=login_params.tenant_id,
        login_type=login_params.login_type,
        identifier=login_params.identifier,
        credential=login_params.credential,
        remember_me=login_params.remember_me,
        mfa_code=login_params.mfa_code,
        device_info=login_params.device_info.dict() if login_params.device_info else None
    )
    return success_response(result, message="登录成功")


@router.post(
    "/logout",
    summary="用户登出",
    description="""
    用户登出

    **功能说明：**
    - 支持单设备登出和全设备登出
    - 自动清理会话和令牌
    - 记录登出时间和原因
    - 更新用户活动状态

    **业务规则：**
    - 验证会话有效性
    - 清理相关缓存数据
    - 记录登出审计日志

    **返回数据：**
    - 登出时间和结果
    - 登出的会话数量
    - 剩余活跃会话数量
    """,
    response_model=LogoutResponseModel,
    responses={
        200: {"description": "登出成功"},
        400: {"description": "请求参数错误"},
        401: {"description": "会话无效"},
        500: {"description": "服务器内部错误"}
    },
    tags=["认证与安全"]
)
@inject
async def logout(
    request: BaseRequest[LogoutRequest],
    auth_service: AuthService = Depends(Provide[ServiceContainer.auth_service])
):
    """
    用户登出

    支持单设备和全设备登出的用户登出功能
    """
    logout_params = request.data
    result = await auth_service.logout(
        tenant_id=logout_params.tenant_id,
        session_id=logout_params.session_id,
        logout_all_devices=logout_params.logout_all_devices
    )
    return success_response(result, message="登出成功")


@router.post(
    "/refresh",
    summary="刷新令牌",
    description="""
    刷新访问令牌

    **功能说明：**
    - 使用刷新令牌获取新的访问令牌
    - 验证刷新令牌有效性
    - 检查会话状态和设备安全
    - 生成新的令牌对

    **业务规则：**
    - 刷新令牌必须有效且未过期
    - 会话必须处于活跃状态
    - 设备信息必须匹配

    **返回数据：**
    - 新的访问令牌和刷新令牌
    - 令牌过期时间
    - 签发时间
    """,
    response_model=RefreshTokenResponseModel,
    responses={
        200: {"description": "令牌刷新成功"},
        400: {"description": "请求参数错误"},
        401: {"description": "刷新令牌无效"},
        403: {"description": "会话已过期"},
        500: {"description": "服务器内部错误"}
    },
    tags=["认证与安全"]
)
@inject
async def refresh_token(
    request: BaseRequest[RefreshTokenRequest],
    auth_service: AuthService = Depends(Provide[ServiceContainer.auth_service])
):
    """
    刷新令牌

    使用刷新令牌获取新的访问令牌
    """
    refresh_params = request.data
    result = await auth_service.refresh_token(
        tenant_id=refresh_params.tenant_id,
        refresh_token=refresh_params.refresh_token,
        session_id=refresh_params.session_id
    )
    return success_response(result, message="令牌刷新成功")


@router.post(
    "/change_password",
    summary="修改密码",
    description="""
    用户修改密码

    **功能说明：**
    - 用户主动修改密码
    - 验证原密码正确性
    - 检查新密码策略合规性
    - 可选择登出其他会话
    - 更新密码过期时间

    **业务规则：**
    - 原密码必须正确
    - 新密码必须符合密码策略
    - 新密码不能与历史密码重复
    - 修改后可强制其他会话下线

    **返回数据：**
    - 密码修改时间
    - 新密码过期时间
    - 终止的会话数量
    """,
    response_model=ChangePasswordResponseModel,
    responses={
        200: {"description": "密码修改成功"},
        400: {"description": "请求参数错误或密码不符合策略"},
        401: {"description": "原密码错误"},
        409: {"description": "新密码与历史密码重复"},
        500: {"description": "服务器内部错误"}
    },
    tags=["认证与安全"]
)
@inject
async def change_password(
    request: BaseRequest[ChangePasswordRequest],
    auth_service: AuthService = Depends(Provide[ServiceContainer.auth_service])
):
    """
    修改密码

    用户主动修改密码功能
    """
    change_password_params = request.data
    result = await auth_service.change_password(
        tenant_id=change_password_params.tenant_id,
        user_id=change_password_params.user_id,
        old_password=change_password_params.old_password,
        new_password=change_password_params.new_password,
        logout_other_sessions=change_password_params.logout_other_sessions
    )
    return success_response(result, message="密码修改成功")


@router.post(
    "/forgot_password",
    summary="忘记密码",
    description="""
    忘记密码处理

    **功能说明：**
    - 用户忘记密码时发起重置流程
    - 支持邮箱和手机号重置
    - 发送重置令牌到指定联系方式
    - 防暴力破解保护

    **业务规则：**
    - 验证用户身份存在性
    - 检查重置频率限制
    - 生成安全的重置令牌
    - 记录重置请求日志

    **返回数据：**
    - 重置令牌ID
    - 令牌过期时间
    - 发送目标（脱敏显示）
    """,
    response_model=ForgotPasswordResponseModel,
    responses={
        200: {"description": "重置密码邮件已发送"},
        400: {"description": "请求参数错误"},
        404: {"description": "用户不存在"},
        429: {"description": "重置频率过高"},
        500: {"description": "服务器内部错误"}
    },
    tags=["认证与安全"]
)
@inject
async def forgot_password(
    request: BaseRequest[ForgotPasswordRequest],
    auth_service: AuthService = Depends(Provide[ServiceContainer.auth_service])
):
    """
    忘记密码

    发起密码重置流程
    """
    forgot_params = request.data
    result = await auth_service.forgot_password(
        tenant_id=forgot_params.tenant_id,
        identifier=forgot_params.identifier,
        identifier_type=forgot_params.identifier_type,
        captcha_token=forgot_params.captcha_token
    )
    return success_response(result, message="重置密码邮件已发送")


@router.post(
    "/reset_password",
    summary="重置密码",
    description="""
    重置密码

    **功能说明：**
    - 通过重置令牌重置密码
    - 验证重置令牌有效性
    - 设置新密码
    - 强制终止所有会话

    **业务规则：**
    - 重置令牌必须有效且未过期
    - 新密码必须符合密码策略
    - 重置后强制所有会话下线
    - 记录密码重置日志

    **返回数据：**
    - 密码重置时间
    - 会话终止状态
    """,
    response_model=ResetPasswordResponseModel,
    responses={
        200: {"description": "密码重置成功"},
        400: {"description": "请求参数错误或重置令牌无效"},
        409: {"description": "密码不符合策略"},
        500: {"description": "服务器内部错误"}
    },
    tags=["认证与安全"]
)
@inject
async def reset_password(
    request: BaseRequest[ResetPasswordRequest],
    auth_service: AuthService = Depends(Provide[ServiceContainer.auth_service])
):
    """
    重置密码

    通过重置令牌重置用户密码
    """
    reset_params = request.data
    result = await auth_service.reset_password(
        reset_token=reset_params.reset_token,
        new_password=reset_params.new_password,
        confirm_password=reset_params.confirm_password
    )
    return success_response(result, message="密码重置成功")


@router.post(
    "/mfa/setup",
    summary="设置多因子认证",
    description="""
    设置多因子认证

    **功能说明：**
    - 初始化MFA设置流程
    - 生成TOTP密钥和二维码
    - 提供备用恢复码
    - 创建设置令牌

    **业务规则：**
    - 用户必须已登录
    - 支持TOTP、短信、邮箱等方式
    - 生成安全的密钥和恢复码
    - 设置令牌有时效性

    **返回数据：**
    - TOTP密钥
    - 二维码URL
    - 备用恢复码
    - 设置令牌
    """,
    response_model=SetupMFAResponseModel,
    responses={
        200: {"description": "MFA设置信息生成成功"},
        400: {"description": "请求参数错误"},
        401: {"description": "用户未登录"},
        409: {"description": "MFA已启用"},
        500: {"description": "服务器内部错误"}
    },
    tags=["认证与安全"]
)
@inject
async def setup_mfa(
    request: BaseRequest[SetupMFARequest],
    auth_service: AuthService = Depends(Provide[ServiceContainer.auth_service])
):
    """
    设置多因子认证

    初始化MFA设置流程
    """
    setup_params = request.data
    result = await auth_service.setup_mfa(
        tenant_id=setup_params.tenant_id,
        user_id=setup_params.user_id,
        mfa_type=setup_params.mfa_type,
        device_name=setup_params.device_name
    )
    return success_response(result, message="MFA设置信息生成成功")


@router.post(
    "/mfa/verify",
    summary="验证并启用MFA",
    description="""
    验证并启用MFA

    **功能说明：**
    - 验证MFA设置的正确性
    - 启用多因子认证
    - 保存MFA配置
    - 记录启用日志

    **业务规则：**
    - 设置令牌必须有效
    - 验证码必须正确
    - 启用后立即生效
    - 记录MFA启用事件

    **返回数据：**
    - MFA启用状态
    - 启用时间
    """,
    response_model=VerifyMFAResponseModel,
    responses={
        200: {"description": "MFA启用成功"},
        400: {"description": "请求参数错误或验证码无效"},
        404: {"description": "设置令牌不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["认证与安全"]
)
@inject
async def verify_mfa(
    request: BaseRequest[VerifyMFARequest],
    auth_service: AuthService = Depends(Provide[ServiceContainer.auth_service])
):
    """
    验证并启用MFA

    验证MFA设置并启用多因子认证
    """
    verify_params = request.data
    result = await auth_service.verify_mfa(
        tenant_id=verify_params.tenant_id,
        user_id=verify_params.user_id,
        setup_token=verify_params.setup_token,
        verification_code=verify_params.verification_code
    )
    return success_response(result, message="MFA启用成功")


@router.post(
    "/sessions/list",
    summary="查看用户会话",
    description="""
    查看用户会话列表

    **功能说明：**
    - 获取用户的所有活跃会话
    - 显示设备信息和活动时间
    - 标识当前会话
    - 可选择包含已过期会话

    **业务规则：**
    - 用户只能查看自己的会话
    - 管理员可以查看指定用户会话
    - 显示设备和地理位置信息
    - 按活动时间排序

    **返回数据：**
    - 活跃会话列表
    - 设备信息和活动时间
    - 当前会话标识
    - 总会话数量
    """,
    response_model=ListSessionsResponseModel,
    responses={
        200: {"description": "查询成功"},
        400: {"description": "请求参数错误"},
        401: {"description": "用户未登录"},
        403: {"description": "权限不足"},
        500: {"description": "服务器内部错误"}
    },
    tags=["认证与安全"]
)
@inject
async def list_sessions(
    request: BaseRequest[ListSessionsRequest],
    auth_service: AuthService = Depends(Provide[ServiceContainer.auth_service])
):
    """
    查看用户会话

    获取用户的活跃会话列表
    """
    list_params = request.data
    result = await auth_service.list_sessions(
        tenant_id=list_params.tenant_id,
        user_id=list_params.user_id,
        include_expired=list_params.include_expired
    )
    return success_response(result, message="查询成功")


@router.post(
    "/sessions/terminate",
    summary="强制下线会话",
    description="""
    强制下线指定会话

    **功能说明：**
    - 强制终止指定的用户会话
    - 清理会话相关数据
    - 记录终止原因和时间
    - 发送安全通知

    **业务规则：**
    - 用户可以终止自己的其他会话
    - 管理员可以终止任意用户会话
    - 不能终止当前会话
    - 记录会话终止日志

    **返回数据：**
    - 被终止的会话ID
    - 终止时间
    - 终止原因
    """,
    response_model=TerminateSessionResponseModel,
    responses={
        200: {"description": "会话已强制下线"},
        400: {"description": "请求参数错误"},
        401: {"description": "用户未登录"},
        403: {"description": "权限不足"},
        404: {"description": "会话不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["认证与安全"]
)
@inject
async def terminate_session(
    request: BaseRequest[TerminateSessionRequest],
    auth_service: AuthService = Depends(Provide[ServiceContainer.auth_service])
):
    """
    强制下线会话

    强制终止指定的用户会话
    """
    terminate_params = request.data
    result = await auth_service.terminate_session(
        tenant_id=terminate_params.tenant_id,
        user_id=terminate_params.user_id,
        session_id=terminate_params.session_id,
        reason=terminate_params.reason
    )
    return success_response(result, message="会话已强制下线")


@router.post(
    "/verify_code",
    summary="验证验证码",
    description="""
    验证各种类型的验证码

    **功能说明：**
    - 验证短信、邮箱、TOTP等验证码
    - 检查验证码有效性和过期时间
    - 记录验证结果
    - 防止重复使用

    **业务规则：**
    - 验证码必须有效且未过期
    - 验证码只能使用一次
    - 验证失败有次数限制
    - 记录验证审计日志

    **返回数据：**
    - 验证结果
    - 验证时间
    """,
    response_model=VerifyCodeResponseModel,
    responses={
        200: {"description": "验证码验证成功"},
        400: {"description": "验证码无效或已过期"},
        429: {"description": "验证失败次数过多"},
        500: {"description": "服务器内部错误"}
    },
    tags=["认证与安全"]
)
@inject
async def verify_code(
    request: BaseRequest[VerifyCodeRequest],
    auth_service: AuthService = Depends(Provide[ServiceContainer.auth_service])
):
    """
    验证验证码

    验证各种类型的验证码
    """
    verify_params = request.data
    result = await auth_service.verify_code(
        code_id=verify_params.code_id,
        verification_code=verify_params.verification_code,
        code_type=verify_params.code_type
    )
    return success_response(result, message="验证码验证成功")


@router.post(
    "/confirm_operation",
    summary="敏感操作确认",
    description="""
    敏感操作二次确认

    **功能说明：**
    - 对敏感操作进行二次确认
    - 支持多种确认方式
    - 生成操作令牌
    - 设置操作时效

    **业务规则：**
    - 敏感操作必须二次确认
    - 确认方式可配置
    - 操作令牌有时效性
    - 记录确认审计日志

    **返回数据：**
    - 操作令牌
    - 令牌过期时间
    - 确认时间
    """,
    response_model=ConfirmOperationResponseModel,
    responses={
        200: {"description": "操作确认成功"},
        400: {"description": "请求参数错误或验证失败"},
        401: {"description": "用户未登录"},
        403: {"description": "权限不足"},
        500: {"description": "服务器内部错误"}
    },
    tags=["认证与安全"]
)
@inject
async def confirm_operation(
    request: BaseRequest[ConfirmOperationRequest],
    auth_service: AuthService = Depends(Provide[ServiceContainer.auth_service])
):
    """
    敏感操作确认

    对敏感操作进行二次确认
    """
    confirm_params = request.data
    result = await auth_service.confirm_operation(
        tenant_id=confirm_params.tenant_id,
        user_id=confirm_params.user_id,
        operation_type=confirm_params.operation_type,
        operation_data=confirm_params.operation_data,
        confirmation_method=confirm_params.confirmation_method,
        verification_code=confirm_params.verification_code
    )
    return success_response(result, message="操作确认成功")









@router.post(
    "/mfa/disable",
    summary="禁用多因子认证",
    description="""
    禁用用户的多因子认证功能

    **功能说明：**
    - 禁用用户的MFA功能
    - 验证用户身份和权限
    - 清理MFA相关配置
    - 记录禁用操作日志
    - 发送安全通知

    **业务规则：**
    - 必须提供正确的用户密码
    - 如果用户仍能生成MFA验证码，需要提供当前验证码
    - 禁用后用户登录不再需要MFA验证
    - 清理所有MFA设备和备用码
    - 记录MFA禁用审计日志

    **安全考虑：**
    - 禁用MFA会降低账户安全性
    - 建议在安全环境下操作
    - 操作后发送安全通知邮件
    - 记录详细的操作日志

    **返回数据：**
    - MFA禁用状态
    - 禁用时间
    - 禁用原因
    """,
    response_model=DisableMFAResponseModel,
    responses={
        200: {
            "description": "MFA禁用成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "MFA已禁用",
                        "data": {
                            "user_id": "user_550e8400-e29b-41d4-a716-446655440000",
                            "mfa_enabled": False,
                            "disabled_at": "2025-01-15T10:30:45.123456",
                            "reason": "用户更换设备，需要重新设置MFA"
                        }
                    }
                }
            }
        },
        400: {"description": "请求参数错误或密码验证失败"},
        401: {"description": "用户未登录"},
        403: {"description": "权限不足"},
        404: {"description": "用户不存在或MFA未启用"},
        500: {"description": "服务器内部错误"}
    },
    tags=["认证与安全"]
)
@inject
async def disable_mfa(
    request: BaseRequest[DisableMFARequest],
    auth_service: AuthService = Depends(Provide[ServiceContainer.auth_service])
):
    """
    禁用多因子认证

    禁用用户的MFA功能，需要密码验证
    """
    disable_mfa_params = request.data
    result = await auth_service.disable_mfa(
        tenant_id=disable_mfa_params.tenant_id,
        user_id=disable_mfa_params.user_id,
        password=disable_mfa_params.password,
        mfa_code=disable_mfa_params.mfa_code,
        reason=disable_mfa_params.reason
    )
    return success_response(result, message="MFA已禁用")


