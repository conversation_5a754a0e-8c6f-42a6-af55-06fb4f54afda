"""
权限管理路由

提供权限的创建、查询、更新、删除等接口
"""

from fastapi import APIRouter, Depends
from dependency_injector.wiring import inject, Provide
from pydantic import BaseModel, Field
from typing import Optional, List

from commonlib.schemas.request import BaseRequest
from commonlib.schemas.responses import SuccessResponse, success_response
from container import ServiceContainer
from services.permission_service import PermissionService

router = APIRouter()


# ===== 请求模型 =====
class CreatePermissionRequest(BaseModel):
    """创建权限请求"""
    permission_name: str = Field(..., description="权限名称", min_length=2, max_length=100, examples=["查看用户"])
    permission_code: str = Field(..., description="权限编码", min_length=2, max_length=100, examples=["user:view"])
    description: Optional[str] = Field(None, description="权限描述", examples=["允许查看用户信息"])
    resource_type: str = Field(..., description="资源类型", examples=["user"])
    action: str = Field(..., description="操作类型", examples=["view"])
    tenant_id: str = Field(..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"])
    parent_id: Optional[str] = Field(None, description="父权限ID", examples=["9436f564-7239-4e55-a66c-65092845d9c4"])


class ListPermissionsRequest(BaseModel):
    """查询权限列表请求"""
    tenant_id: str = Field(..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"])
    limit: int = Field(20, description="每页数量", ge=1, le=100, examples=[20])
    search: Optional[str] = Field(None, description="搜索关键词", examples=["用户"])
    resource_type: Optional[str] = Field(None, description="资源类型筛选", examples=["user"])
    parent_id: Optional[str] = Field(None, description="父权限ID筛选", examples=["9436f564-7239-4e55-a66c-65092845d9c4"])


class GetPermissionRequest(BaseModel):
    """获取权限详情请求"""
    permission_id: str = Field(..., description="权限ID", examples=["9436f564-7239-4e55-a66c-65092845d9c4"])
    tenant_id: str = Field(..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"])


class UpdatePermissionRequest(BaseModel):
    """更新权限请求"""
    permission_id: str = Field(..., description="权限ID", examples=["9436f564-7239-4e55-a66c-65092845d9c4"])
    tenant_id: str = Field(..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"])
    permission_name: Optional[str] = Field(None, description="权限名称", examples=["编辑用户"])
    description: Optional[str] = Field(None, description="权限描述", examples=["允许编辑用户信息"])
    status: Optional[str] = Field(None, description="权限状态", examples=["active"])


class DeletePermissionRequest(BaseModel):
    """删除权限请求"""
    permission_id: str = Field(..., description="权限ID", examples=["9436f564-7239-4e55-a66c-65092845d9c4"])
    tenant_id: str = Field(..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"])
    force: bool = Field(False, description="是否强制删除", examples=[False])


class GetPermissionTreeRequest(BaseModel):
    """获取权限树请求"""
    tenant_id: str = Field(..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"])
    resource_type: Optional[str] = Field(None, description="资源类型筛选", examples=["user"])


# ===== 响应数据模型 =====
class PermissionResponse(BaseModel):
    """权限响应数据"""
    permission_id: str = Field(..., description="权限ID", examples=["9436f564-7239-4e55-a66c-65092845d9c4"])
    permission_name: str = Field(..., description="权限名称", examples=["查看用户"])
    permission_code: str = Field(..., description="权限编码", examples=["user:view"])
    description: Optional[str] = Field(None, description="权限描述", examples=["允许查看用户信息"])
    resource_type: str = Field(..., description="资源类型", examples=["user"])
    action: str = Field(..., description="操作类型", examples=["view"])
    status: str = Field(..., description="权限状态", examples=["active"])
    tenant_id: str = Field(..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"])
    parent_id: Optional[str] = Field(None, description="父权限ID", examples=["perm_root"])
    level: int = Field(..., description="权限层级", examples=[1])
    children: Optional[List["PermissionResponse"]] = Field([], description="子权限列表", examples=[[]])
    created_at: str = Field(..., description="创建时间", examples=["2025-07-25T10:00:00"])
    updated_at: str = Field(..., description="更新时间", examples=["2025-07-25T10:30:00"])


class PermissionListResponse(BaseModel):
    """权限列表响应数据"""
    permissions: List[PermissionResponse] = Field(..., description="权限列表")
    total: int = Field(..., description="总数量", examples=[100])


class PermissionTreeResponse(BaseModel):
    """权限树响应数据"""
    tree: List[PermissionResponse] = Field(..., description="权限树结构")
    total: int = Field(..., description="总权限数量", examples=[20])


class PermissionOperationResponse(BaseModel):
    """权限操作响应数据"""
    success: bool = Field(True, description="操作是否成功", examples=[True])
    permission_id: Optional[str] = Field(None, description="权限ID", examples=["9436f564-7239-4e55-a66c-65092845d9c4"])


# ===== 响应模型 =====
class PermissionResponseModel(SuccessResponse[PermissionResponse]):
    """权限响应模型"""
    data: PermissionResponse


class PermissionListResponseModel(SuccessResponse[PermissionListResponse]):
    """权限列表响应模型"""
    data: PermissionListResponse


class PermissionTreeResponseModel(SuccessResponse[PermissionTreeResponse]):
    """权限树响应模型"""
    data: PermissionTreeResponse


class PermissionOperationResponseModel(SuccessResponse[PermissionOperationResponse]):
    """权限操作响应模型"""
    data: PermissionOperationResponse


# ===== 路由端点 =====
@router.post(
    "/create",
    summary="创建权限",
    description="在指定租户下创建新权限",
    response_model=PermissionResponseModel,
)
@inject
async def create_permission(
    request: BaseRequest[CreatePermissionRequest],
    permission_service: PermissionService = Depends(Provide[ServiceContainer.permission_service])
):
    """创建权限"""
    create_permission_params = request.data
    result = await permission_service.create_permission(
        permission_name=create_permission_params.permission_name,
        permission_code=create_permission_params.permission_code,
        description=create_permission_params.description,
        resource_type=create_permission_params.resource_type,
        action=create_permission_params.action,
        tenant_id=create_permission_params.tenant_id,
        parent_id=create_permission_params.parent_id
    )
    return success_response(result, message="权限创建成功")


@router.post(
    "/list",
    summary="查询权限列表",
    description="分页查询租户下的权限列表，支持搜索和筛选",
    response_model=PermissionListResponseModel,
)
@inject
async def list_permissions(
    request: BaseRequest[ListPermissionsRequest],
    permission_service: PermissionService = Depends(Provide[ServiceContainer.permission_service])
):
    """查询权限列表"""
    list_permissions_params = request.data
    result = await permission_service.list_permissions(
        tenant_id=list_permissions_params.tenant_id,
        limit=list_permissions_params.limit,
        search=list_permissions_params.search,
        resource_type=list_permissions_params.resource_type,
        parent_id=list_permissions_params.parent_id
    )
    return success_response(result, message="查询成功")


@router.post(
    "/detail",
    summary="获取权限详情",
    description="获取指定权限的详细信息",
    response_model=PermissionResponseModel,
)
@inject
async def get_permission_detail(
    request: BaseRequest[GetPermissionRequest],
    permission_service: PermissionService = Depends(Provide[ServiceContainer.permission_service])
):
    """获取权限详情"""
    get_permission_params = request.data
    result = await permission_service.get_permission_detail(
        permission_id=get_permission_params.permission_id,
        tenant_id=get_permission_params.tenant_id
    )
    return success_response(result, message="查询成功")


@router.post(
    "/update",
    summary="更新权限信息",
    description="更新权限的基本信息",
    response_model=PermissionResponseModel,
)
@inject
async def update_permission(
    request: BaseRequest[UpdatePermissionRequest],
    permission_service: PermissionService = Depends(Provide[ServiceContainer.permission_service])
):
    """更新权限信息"""
    update_permission_params = request.data
    result = await permission_service.update_permission(
        permission_id=update_permission_params.permission_id,
        tenant_id=update_permission_params.tenant_id,
        permission_name=update_permission_params.permission_name,
        description=update_permission_params.description,
        status=update_permission_params.status
    )
    return success_response(result, message="权限信息更新成功")


@router.post(
    "/delete",
    summary="删除权限",
    description="删除指定权限，支持软删除和强制删除",
    response_model=PermissionOperationResponseModel,
)
@inject
async def delete_permission(
    request: BaseRequest[DeletePermissionRequest],
    permission_service: PermissionService = Depends(Provide[ServiceContainer.permission_service])
):
    """删除权限"""
    delete_permission_params = request.data
    result = await permission_service.delete_permission(
        permission_id=delete_permission_params.permission_id,
        tenant_id=delete_permission_params.tenant_id,
        force=delete_permission_params.force
    )
    return success_response(result, message="权限删除成功")


@router.post(
    "/tree",
    summary="获取权限树",
    description="获取租户下的权限树结构",
    response_model=PermissionTreeResponseModel,
)
@inject
async def get_permission_tree(
    request: BaseRequest[GetPermissionTreeRequest],
    permission_service: PermissionService = Depends(Provide[ServiceContainer.permission_service])
):
    """获取权限树"""
    get_permission_tree_params = request.data
    result = await permission_service.get_permission_tree(
        tenant_id=get_permission_tree_params.tenant_id,
        resource_type=get_permission_tree_params.resource_type
    )
    return success_response(result, message="权限树查询成功")



