"""
知识库管理路由

提供知识库的创建、查询、更新、删除等接口
"""

from fastapi import APIRouter, Depends
from dependency_injector.wiring import inject, Provide
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

from commonlib.schemas.request import BaseRequest
from commonlib.schemas.responses import SuccessResponse, success_response
from container import ServiceContainer
from services.knowledge_base_service import KnowledgeBaseService

router = APIRouter()


# ===== 请求模型 =====
class CreateKnowledgeBaseRequest(BaseModel):
    """创建知识库请求"""
    kb_name: str = Field(..., description="知识库名称", min_length=2, max_length=100)
    kb_code: str = Field(..., description="知识库编码", min_length=2, max_length=50)
    description: Optional[str] = Field(None, description="知识库描述")
    tenant_id: str = Field(..., description="租户ID")
    config: Optional[Dict[str, Any]] = Field({}, description="知识库配置")
    embedding_model: Optional[str] = Field(None, description="嵌入模型")
    vector_dimension: Optional[int] = Field(None, description="向量维度")
    access_level: str = Field("private", description="访问级别: public, private, restricted")


class ListKnowledgeBasesRequest(BaseModel):
    """查询知识库列表请求"""
    tenant_id: str = Field(..., description="租户ID")
    user_id: Optional[str] = Field(None, description="用户ID(用于权限筛选)")
    cursor: Optional[str] = Field(None, description="分页游标")
    limit: int = Field(20, description="每页数量", ge=1, le=100)
    search: Optional[str] = Field(None, description="搜索关键词")
    access_level: Optional[str] = Field(None, description="访问级别筛选")


class GetKnowledgeBaseRequest(BaseModel):
    """获取知识库详情请求"""
    kb_id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    user_id: Optional[str] = Field(None, description="用户ID(用于权限检查)")


class UpdateKnowledgeBaseRequest(BaseModel):
    """更新知识库请求"""
    kb_id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    kb_name: Optional[str] = Field(None, description="知识库名称")
    description: Optional[str] = Field(None, description="知识库描述")
    config: Optional[Dict[str, Any]] = Field(None, description="知识库配置")
    access_level: Optional[str] = Field(None, description="访问级别")
    status: Optional[str] = Field(None, description="知识库状态")


class DeleteKnowledgeBaseRequest(BaseModel):
    """删除知识库请求"""
    kb_id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    force: bool = Field(False, description="是否强制删除")


class GrantAccessRequest(BaseModel):
    """授权访问请求"""
    kb_id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    user_id: str = Field(..., description="用户ID")
    access_type: str = Field(..., description="访问类型: read, write, admin")


class RevokeAccessRequest(BaseModel):
    """撤销访问请求"""
    kb_id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    user_id: str = Field(..., description="用户ID")


# ===== 响应数据模型 =====
class KnowledgeBaseResponse(BaseModel):
    """知识库响应数据"""
    kb_id: str = Field(..., description="知识库ID")
    kb_name: str = Field(..., description="知识库名称")
    kb_code: str = Field(..., description="知识库编码")
    description: Optional[str] = Field(None, description="知识库描述")
    status: str = Field(..., description="知识库状态")
    tenant_id: str = Field(..., description="租户ID")
    config: Dict[str, Any] = Field({}, description="知识库配置")
    embedding_model: Optional[str] = Field(None, description="嵌入模型")
    vector_dimension: Optional[int] = Field(None, description="向量维度")
    access_level: str = Field(..., description="访问级别")
    document_count: int = Field(0, description="文档数量")
    vector_count: int = Field(0, description="向量数量")
    user_access: Optional[str] = Field(None, description="当前用户访问权限")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class KnowledgeBaseListResponse(BaseModel):
    """知识库列表响应数据"""
    knowledge_bases: List[KnowledgeBaseResponse] = Field(..., description="知识库列表")
    total: int = Field(..., description="总数量")
    next_cursor: Optional[str] = Field(None, description="下一页游标")
    has_more: bool = Field(..., description="是否有更多数据")


class KnowledgeBaseOperationResponse(BaseModel):
    """知识库操作响应数据"""
    success: bool = Field(True, description="操作是否成功")
    kb_id: Optional[str] = Field(None, description="知识库ID")


# ===== 响应模型 =====
class KnowledgeBaseResponseModel(SuccessResponse[KnowledgeBaseResponse]):
    """知识库响应模型"""
    data: KnowledgeBaseResponse


class KnowledgeBaseListResponseModel(SuccessResponse[KnowledgeBaseListResponse]):
    """知识库列表响应模型"""
    data: KnowledgeBaseListResponse


class KnowledgeBaseOperationResponseModel(SuccessResponse[KnowledgeBaseOperationResponse]):
    """知识库操作响应模型"""
    data: KnowledgeBaseOperationResponse


# ===== 路由端点 =====
@router.post(
    "/create",
    summary="创建知识库",
    description="在指定租户下创建新的知识库",
    response_model=KnowledgeBaseResponseModel,
)
@inject
async def create_knowledge_base(
    request: BaseRequest[CreateKnowledgeBaseRequest],
    kb_service: KnowledgeBaseService = Depends(Provide[ServiceContainer.knowledge_base_service])
):
    """创建知识库"""
    create_kb_params = request.data
    result = await kb_service.create_knowledge_base(
        kb_name=create_kb_params.kb_name,
        kb_code=create_kb_params.kb_code,
        description=create_kb_params.description,
        tenant_id=create_kb_params.tenant_id,
        config=create_kb_params.config,
        embedding_model=create_kb_params.embedding_model,
        vector_dimension=create_kb_params.vector_dimension,
        access_level=create_kb_params.access_level
    )
    return success_response(result, message="知识库创建成功")


@router.post(
    "/list",
    summary="查询知识库列表",
    description="分页查询用户可访问的知识库列表，支持搜索和筛选",
    response_model=KnowledgeBaseListResponseModel,
)
@inject
async def list_knowledge_bases(
    request: BaseRequest[ListKnowledgeBasesRequest],
    kb_service: KnowledgeBaseService = Depends(Provide[ServiceContainer.knowledge_base_service])
):
    """查询知识库列表"""
    list_kb_params = request.data
    result = await kb_service.list_knowledge_bases(
        tenant_id=list_kb_params.tenant_id,
        user_id=list_kb_params.user_id,
        cursor=list_kb_params.cursor,
        limit=list_kb_params.limit,
        search=list_kb_params.search,
        access_level=list_kb_params.access_level
    )
    return success_response(result, message="查询成功")


@router.post(
    "/detail",
    summary="获取知识库详情",
    description="获取指定知识库的详细信息，包括配置和统计数据",
    response_model=KnowledgeBaseResponseModel,
)
@inject
async def get_knowledge_base_detail(
    request: BaseRequest[GetKnowledgeBaseRequest],
    kb_service: KnowledgeBaseService = Depends(Provide[ServiceContainer.knowledge_base_service])
):
    """获取知识库详情"""
    get_kb_params = request.data
    result = await kb_service.get_knowledge_base_detail(
        kb_id=get_kb_params.kb_id,
        tenant_id=get_kb_params.tenant_id,
        user_id=get_kb_params.user_id
    )
    return success_response(result, message="查询成功")


@router.post(
    "/update",
    summary="更新知识库信息",
    description="更新知识库的基本信息和配置",
    response_model=KnowledgeBaseResponseModel,
)
@inject
async def update_knowledge_base(
    request: BaseRequest[UpdateKnowledgeBaseRequest],
    kb_service: KnowledgeBaseService = Depends(Provide[ServiceContainer.knowledge_base_service])
):
    """更新知识库信息"""
    update_kb_params = request.data
    result = await kb_service.update_knowledge_base(
        kb_id=update_kb_params.kb_id,
        tenant_id=update_kb_params.tenant_id,
        kb_name=update_kb_params.kb_name,
        description=update_kb_params.description,
        config=update_kb_params.config,
        access_level=update_kb_params.access_level,
        status=update_kb_params.status
    )
    return success_response(result, message="知识库信息更新成功")


@router.post(
    "/delete",
    summary="删除知识库",
    description="删除指定知识库，支持软删除和强制删除",
    response_model=KnowledgeBaseOperationResponseModel,
)
@inject
async def delete_knowledge_base(
    request: BaseRequest[DeleteKnowledgeBaseRequest],
    kb_service: KnowledgeBaseService = Depends(Provide[ServiceContainer.knowledge_base_service])
):
    """删除知识库"""
    delete_kb_params = request.data
    result = await kb_service.delete_knowledge_base(
        kb_id=delete_kb_params.kb_id,
        tenant_id=delete_kb_params.tenant_id,
        force=delete_kb_params.force
    )
    return success_response(result, message="知识库删除成功")


@router.post(
    "/grant_access",
    summary="授权知识库访问",
    description="为用户授权访问指定知识库的权限",
    response_model=KnowledgeBaseOperationResponseModel,
)
@inject
async def grant_access(
    request: BaseRequest[GrantAccessRequest],
    kb_service: KnowledgeBaseService = Depends(Provide[ServiceContainer.knowledge_base_service])
):
    """授权知识库访问"""
    grant_access_params = request.data
    result = await kb_service.grant_access(
        kb_id=grant_access_params.kb_id,
        tenant_id=grant_access_params.tenant_id,
        user_id=grant_access_params.user_id,
        access_type=grant_access_params.access_type
    )
    return success_response(result, message="访问权限授权成功")


@router.post(
    "/revoke_access",
    summary="撤销知识库访问",
    description="撤销用户对指定知识库的访问权限",
    response_model=KnowledgeBaseOperationResponseModel,
)
@inject
async def revoke_access(
    request: BaseRequest[RevokeAccessRequest],
    kb_service: KnowledgeBaseService = Depends(Provide[ServiceContainer.knowledge_base_service])
):
    """撤销知识库访问"""
    revoke_access_params = request.data
    result = await kb_service.revoke_access(
        kb_id=revoke_access_params.kb_id,
        tenant_id=revoke_access_params.tenant_id,
        user_id=revoke_access_params.user_id
    )
    return success_response(result, message="访问权限撤销成功")



