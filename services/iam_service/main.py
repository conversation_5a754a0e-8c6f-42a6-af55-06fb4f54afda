"""
IAM 服务应用入口

基于项目统一的 DI 容器设计，提供用户与权限管理服务
"""


from container import ServiceContainer
from tasks.scheduler import dispatch_scheduler
from routes import api_router
from commonlib.core.containers.config_container import (ConfigContainer,
                                                        set_up_config_di)
from commonlib.core.containers.infra_container import InfraContainer
from domain_common.app_builder.default_app_factory import AppInitializer

# 初始化依赖注入容器
config: ConfigContainer = set_up_config_di()
infra = InfraContainer(config=config)
services = ServiceContainer(config=config, infra=infra)

# 初始化任务调度器
scheduler_modules = dispatch_scheduler()
wire_services = [
    "routes.tenants", "services.tenant_service",
    "routes.users", "services.user_service",
    "routes.auth", "services.auth_service",
    "routes.rbac", "services.rbac_service",
    "routes.roles", "services.role_service",
    "routes.permissions", "services.permission_service",
    "routes.audit", "services.audit_service",
    "routes.advanced_security", "services.advanced_security_service",
    "routes.system", "services.system_service",
    "routes.system_config", "services.system_config_service",
    "tasks"
]
# 创建 FastAPI 应用
app = AppInitializer(
    config, infra, services, wire_modules=[*scheduler_modules,*wire_services]
).create_app()

# 注册路由
app.include_router(api_router)



if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8089, log_config=None)
