# 高级安全服务实现说明

## 概述

高级安全服务提供了企业级的安全功能，包括多因子认证(MFA)、安全策略管理、威胁检测和安全事件管理等功能，是IAM系统的核心安全组件。

## 已实现功能

### 1. 多因子认证(MFA)管理
- ✅ **设置MFA** (`setup_mfa`)
  - 支持TOTP、SMS、Email三种MFA类型
  - 自动生成TOTP密钥和二维码
  - 生成备用恢复码
  - 30分钟设置令牌有效期

- ✅ **验证MFA设置** (`verify_mfa_setup`)
  - 验证TOTP验证码
  - 激活MFA功能
  - 存储MFA配置到数据库
  - 创建安全事件记录

- ✅ **禁用MFA** (`disable_mfa`)
  - 需要当前MFA验证码确认
  - 清除备用恢复码
  - 记录禁用原因
  - 创建安全事件

- ✅ **生成备用恢复码** (`generate_backup_codes`)
  - 生成指定数量的恢复码
  - 格式为XXXX-XXXX
  - 1年有效期
  - Redis存储管理

### 2. 安全策略管理
- ✅ **设置安全策略** (`set_security_policy`)
  - 支持租户级和全局策略
  - 动态策略配置
  - 策略版本管理
  - 实时生效

- 🔄 **策略类型支持** - 部分实现
  - password_policy: 密码策略
  - session_policy: 会话策略
  - login_policy: 登录策略
  - access_policy: 访问策略

### 3. 安全事件管理
- ✅ **查询安全事件** (`query_security_events`)
  - 多条件过滤查询
  - 分页查询支持
  - 时间范围查询
  - 严重程度分级

- ✅ **更新安全事件** (`update_security_event`)
  - 更新处理状态
  - 分配处理人员
  - 添加处理备注
  - 记录处理历史

- ✅ **创建安全事件** (`_create_security_event`)
  - 自动事件检测
  - 威胁分级评估
  - 详细事件信息
  - 实时告警

### 4. 安全工具集
- ✅ **TOTP工具**
  - 密钥生成 (`_generate_totp_secret`)
  - 二维码生成 (`_generate_totp_qr_url`)
  - 验证码验证 (`_verify_totp`)
  - 时间窗口容错

- ✅ **备用恢复码工具**
  - 安全随机生成 (`_generate_backup_code`)
  - 防混淆字符集
  - 格式化输出
  - Redis存储管理

### 5. 审计功能
- ✅ **审计日志记录**
  - 使用AuditLogBuilder
  - 完整操作记录
  - 审计失败容错
  - 异步处理

## 数据模型适配

### UserMFA模型
- 使用 `secret_key` 字段存储TOTP密钥
- 使用 `backup_codes` 字段存储备用恢复码
- 支持多种MFA类型
- 启用状态管理

### SecurityPolicy模型
- 支持租户级和全局策略
- JSON格式策略配置
- 策略版本管理
- 启用状态控制

### SecurityEvent模型
- 完整的事件信息记录
- 严重程度分级
- 处理状态跟踪
- 详细事件上下文

## 使用示例

### 设置TOTP MFA
```python
result = await security_service.setup_mfa(
    tenant_id="tenant_123",
    user_id="user_123",
    mfa_type="totp"
)

print(f"设置令牌: {result['setup_token']}")
print(f"二维码URL: {result['qr_code_url']}")
print(f"备用恢复码: {result['backup_codes']}")
```

### 验证MFA设置
```python
result = await security_service.verify_mfa_setup(
    tenant_id="tenant_123",
    user_id="user_123",
    setup_token="setup_token_123",
    verification_code="123456"
)

print(f"MFA启用状态: {result['mfa_enabled']}")
```

### 设置安全策略
```python
result = await security_service.set_security_policy(
    tenant_id="tenant_123",
    policy_name="password_policy",
    policy_config={
        "min_length": 8,
        "require_uppercase": True,
        "require_lowercase": True,
        "require_numbers": True,
        "max_age_days": 90
    },
    enabled=True
)
```

### 查询安全事件
```python
result = await security_service.query_security_events(
    tenant_id="tenant_123",
    event_type="login_anomaly",
    severity="high",
    start_time="2025-01-01T00:00:00",
    end_time="2025-01-31T23:59:59",
    page=1,
    page_size=20
)

for event in result['events']:
    print(f"事件: {event['title']} - {event['severity']}")
```

## TODO 功能

### 1. SMS/Email MFA实现
- 短信验证码发送
- 邮箱验证码发送
- 验证码验证逻辑
- 发送频率限制

### 2. 策略配置验证
- 密码策略验证器
- 会话策略验证器
- 登录策略验证器
- 策略冲突检测

### 3. 威胁检测引擎
- 异常登录检测
- 暴力破解检测
- 权限提升检测
- 行为分析引擎

### 4. 高级安全功能
- 设备指纹识别
- 地理位置验证
- 风险评分系统
- 自适应认证

### 5. 集成功能
- 第三方MFA提供商
- SIEM系统集成
- 告警通知系统
- 安全报告生成

## 技术特点

1. **多因子认证支持** - TOTP、SMS、Email多种方式
2. **企业级安全策略** - 灵活的策略配置和管理
3. **实时威胁检测** - 自动检测和响应安全威胁
4. **完整审计追踪** - 所有安全操作的完整记录
5. **高可用设计** - Redis缓存和数据库双重存储
6. **扩展性架构** - 支持插件式安全功能扩展

## 安全考虑

1. **密钥安全** - TOTP密钥安全存储和传输
2. **验证码安全** - 时间窗口和重放攻击防护
3. **会话安全** - 安全的令牌管理和过期机制
4. **数据加密** - 敏感数据的加密存储
5. **访问控制** - 严格的权限验证和授权

## 依赖

- SQLAlchemy (异步ORM)
- Redis (缓存和会话存储)
- pyotp (TOTP实现)
- qrcode (二维码生成)
- CommonLib (异常处理、响应模型)
- Domain Models (数据模型)

## 文件结构

```
services/iam_service/
├── services/
│   ├── advanced_security_service.py       # 高级安全服务实现
│   └── README_ADVANCED_SECURITY.md        # 本文档
├── routes/
│   └── advanced_security.py               # 高级安全路由
├── tests/
│   └── test_advanced_security_service.py  # 高级安全服务测试
└── examples/
    └── advanced_security_usage_example.py # 使用示例
```

## 注意事项

1. **MFA设置流程** - 需要用户使用TOTP应用扫描二维码
2. **备用恢复码** - 用户需要安全保存备用恢复码
3. **策略生效** - 安全策略设置后立即生效
4. **事件处理** - 安全事件需要及时处理和响应
5. **缓存管理** - 注意Redis缓存的TTL设置和清理

高级安全服务已经可以正常使用，提供了企业级的安全功能。对于标注为TODO的功能，可以根据实际需求逐步实现。
