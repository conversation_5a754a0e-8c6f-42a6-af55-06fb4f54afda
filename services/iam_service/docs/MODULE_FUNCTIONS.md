# IAM服务模块功能清单

## 概述

本文档详细列出了IAM服务中所有模块的功能和API接口，为开发者和用户提供完整的功能参考。

## 1. 租户管理模块

### 服务类: `TenantService`
### 路由文件: `routes/tenants.py`
### 路由前缀: `/api/v1/tenants`

#### 核心功能
- **租户创建**: 创建新租户，设置基本信息和配置
- **租户查询**: 支持多条件查询和分页，包括状态过滤
- **租户更新**: 更新租户基本信息和配置
- **租户删除**: 软删除租户，保留历史数据
- **状态管理**: 租户激活、暂停、恢复操作
- **配置管理**: 租户级功能开关和资源限制配置
- **统计分析**: 租户用户数量、活跃度等统计信息

#### API接口
- `POST /create` - 创建租户
- `POST /query` - 查询租户列表
- `POST /get` - 获取租户详情
- `POST /update` - 更新租户信息
- `POST /delete` - 删除租户
- `POST /activate` - 激活租户
- `POST /suspend` - 暂停租户
- `POST /config/update` - 更新租户配置
- `POST /stats` - 获取租户统计信息

## 2. 用户管理模块

### 服务类: `UserService`
### 路由文件: `routes/users.py`
### 路由前缀: `/api/v1/users`

#### 核心功能
- **用户注册**: 支持邮箱、手机号注册，发送激活验证
- **用户创建**: 管理员直接创建用户账户
- **用户激活**: 通过激活令牌激活用户账户
- **用户查询**: 多条件查询用户，支持分页和排序
- **用户更新**: 更新用户基本信息和偏好设置
- **用户删除**: 软删除用户，保留历史数据
- **密码管理**: 密码修改、重置、策略验证
- **状态管理**: 用户启用、禁用、锁定、解锁
- **偏好设置**: 用户个人偏好和配置管理
- **统计分析**: 用户活跃度、登录统计等

#### API接口
- `POST /register` - 用户注册
- `POST /create` - 创建用户
- `POST /activate` - 激活用户
- `POST /query` - 查询用户列表
- `POST /get` - 获取用户详情
- `POST /update` - 更新用户信息
- `POST /delete` - 删除用户
- `POST /enable` - 启用用户
- `POST /disable` - 禁用用户
- `POST /lock` - 锁定用户
- `POST /unlock` - 解锁用户
- `POST /preferences/update` - 更新用户偏好
- `POST /stats` - 获取用户统计

## 3. 认证和安全模块

### 服务类: `AuthService`
### 路由文件: `routes/auth.py`
### 路由前缀: `/api/v1/auth`

#### 核心功能
- **用户登录**: 支持用户名、邮箱、手机号多种登录方式
- **令牌管理**: JWT访问令牌和刷新令牌生成、验证、刷新
- **会话管理**: 用户会话创建、查询、终止、并发控制
- **密码管理**: 密码修改、重置、强度验证
- **验证码管理**: 短信、邮箱验证码生成和验证
- **设备管理**: 设备指纹生成、设备信息记录
- **安全验证**: 敏感操作二次验证
- **登录控制**: 登录频率限制、失败锁定
- **会话监控**: 异常登录检测、并发会话管理

#### API接口
- `POST /login` - 用户登录
- `POST /logout` - 用户登出
- `POST /refresh` - 刷新令牌
- `POST /verify-token` - 验证令牌
- `POST /change-password` - 修改密码
- `POST /reset-password` - 重置密码
- `POST /send-verification` - 发送验证码
- `POST /verify-code` - 验证验证码
- `POST /sessions/query` - 查询用户会话
- `POST /sessions/terminate` - 终止会话
- `POST /confirm-operation` - 确认敏感操作

## 4. 角色权限管理模块 (RBAC)

### 服务类: `RBACService`
### 路由文件: `routes/rbac.py`
### 路由前缀: `/api/v1/rbac`

#### 核心功能
- **角色管理**: 创建、查询、更新、删除角色
- **权限管理**: 创建权限定义、权限分类管理
- **角色权限**: 为角色分配、移除、替换权限
- **用户角色**: 为用户分配、移除、替换角色
- **权限检查**: 检查用户是否具有指定权限
- **角色继承**: 支持角色层级和权限继承
- **批量操作**: 批量权限分配和角色管理
- **权限缓存**: 权限信息缓存优化

#### API接口
- `POST /roles/create` - 创建角色
- `POST /roles/query` - 查询角色列表
- `POST /roles/update` - 更新角色
- `POST /roles/delete` - 删除角色
- `POST /permissions/create` - 创建权限
- `POST /roles/permissions/assign` - 分配角色权限
- `POST /users/roles/assign` - 分配用户角色
- `POST /permissions/check` - 权限检查

## 5. 审计日志系统

### 服务类: `AuditService`
### 路由文件: `routes/audit.py`
### 路由前缀: `/api/v1/audit`

#### 核心功能
- **日志记录**: 自动记录系统操作和安全事件
- **日志查询**: 多条件查询、时间范围过滤、分页查询
- **统计分析**: 操作统计、趋势分析、风险分布
- **日志导出**: 支持CSV、JSON、Excel格式导出
- **风险分级**: 操作风险级别分类和告警
- **用户追踪**: 用户操作轨迹追踪分析
- **异常检测**: 异常操作模式检测

#### API接口
- `POST /logs/query` - 查询审计日志
- `POST /logs/create` - 创建审计日志
- `POST /statistics` - 获取审计统计
- `POST /logs/export` - 导出审计日志

## 6. 系统配置模块

### 服务类: `SystemConfigService`
### 路由文件: `routes/system_config.py`
### 路由前缀: `/api/v1/system`

#### 核心功能
- **配置管理**: 设置、获取、删除系统配置
- **配置继承**: 全局配置和租户级配置继承机制
- **敏感配置**: 敏感配置加密存储和脱敏显示
- **配置分类**: 按功能分类管理配置项
- **批量操作**: 批量设置和重置配置
- **配置验证**: 配置值格式和范围验证
- **配置缓存**: 配置信息缓存优化
- **配置历史**: 配置变更历史记录

#### API接口
- `POST /config/set` - 设置配置
- `POST /config/get` - 获取配置
- `POST /config/query` - 查询配置列表
- `POST /config/delete` - 删除配置
- `POST /config/batch-set` - 批量设置配置
- `POST /config/reset` - 重置配置

## 7. 高级安全功能

### 服务类: `AdvancedSecurityService`
### 路由文件: `routes/advanced_security.py`
### 路由前缀: `/api/v1/security`

#### 核心功能
- **多因子认证**: TOTP、SMS、Email多种MFA方式
- **MFA管理**: MFA设置、验证、禁用、备用恢复码
- **安全策略**: 密码策略、会话策略、登录策略配置
- **威胁检测**: 异常登录、暴力破解、权限提升检测
- **安全事件**: 安全事件记录、处理、状态管理
- **风险评估**: 操作风险评估和分级
- **安全告警**: 高风险操作自动告警

#### API接口
- `POST /mfa/setup` - 设置MFA
- `POST /mfa/verify-setup` - 验证MFA设置
- `POST /mfa/disable` - 禁用MFA
- `POST /mfa/backup-codes` - 生成备用恢复码
- `POST /policy/set` - 设置安全策略
- `POST /events/query` - 查询安全事件
- `POST /events/update` - 更新安全事件

## 8. 知识库管理模块

### 服务类: `KnowledgeBaseService`
### 路由文件: `routes/knowledge_bases.py`
### 路由前缀: `/api/v1/knowledge_bases`

#### 核心功能
- **知识库管理**: 创建、查询、更新、删除知识库
- **访问控制**: 知识库访问权限管理
- **内容管理**: 知识库内容组织和管理
- **搜索功能**: 知识库内容搜索和检索
- **版本控制**: 知识库版本管理
- **统计分析**: 知识库使用统计和分析

## 9. 文档管理模块

### 服务类: `DocumentService`
### 路由文件: `routes/documents.py`
### 路由前缀: `/api/v1/documents`

#### 核心功能
- **文档管理**: 文档上传、下载、删除
- **权限控制**: 文档访问权限管理
- **版本管理**: 文档版本控制和历史记录
- **分类管理**: 文档分类和标签管理
- **搜索功能**: 文档内容搜索和检索
- **存储管理**: 文档存储和备份管理

## 10. 外部服务集成

### 邮件服务 (`EmailService`)
- **邮件发送**: 支持HTML和纯文本邮件
- **模板管理**: 邮件模板管理和渲染
- **异步发送**: 异步邮件发送队列
- **发送记录**: 邮件发送历史和状态跟踪
- **配置管理**: SMTP服务器配置和测试

### 短信服务 (`SMSService`)
- **多厂商支持**: 阿里云、腾讯云等SMS服务
- **模板管理**: 短信模板管理和参数替换
- **发送控制**: 发送频率限制和防刷机制
- **状态跟踪**: 短信发送状态和回执处理
- **成本控制**: 短信发送成本统计和控制

### 验证码服务 (`VerificationService`)
- **验证码生成**: 数字验证码生成和管理
- **多渠道支持**: 短信、邮箱验证码发送
- **场景管理**: 不同业务场景的验证码配置
- **安全控制**: 验证码防刷和安全验证
- **统计分析**: 验证码使用统计和成功率分析

## 11. 安全基础设施

### 安全工具 (`SecurityUtils`)
- **密码处理**: 密码加密、验证、强度检查
- **令牌生成**: 各种安全令牌和密钥生成
- **TOTP支持**: TOTP密钥生成、二维码生成、验证
- **设备指纹**: 设备指纹生成和验证
- **数据脱敏**: 敏感数据脱敏处理
- **加密解密**: 数据加密解密工具

### JWT管理 (`JWTManager`)
- **令牌生成**: JWT访问令牌和刷新令牌生成
- **令牌验证**: JWT签名验证和载荷解析
- **黑名单管理**: 令牌黑名单和撤销机制
- **过期管理**: 令牌过期检查和自动清理
- **自定义载荷**: 支持自定义令牌载荷

### 会话管理 (`SessionManager`)
- **会话创建**: 用户会话创建和初始化
- **会话存储**: 分布式会话存储和同步
- **并发控制**: 用户并发会话数量限制
- **设备追踪**: 登录设备信息记录和管理
- **异常检测**: 异常登录行为检测
- **自动清理**: 过期会话自动清理

### 缓存管理 (`CacheManager`)
- **多级缓存**: 内存缓存和Redis缓存
- **缓存策略**: 不同数据的缓存策略配置
- **缓存更新**: 缓存数据更新和失效机制
- **性能优化**: 缓存命中率优化
- **监控统计**: 缓存使用统计和性能监控

## 12. 后台任务系统

### 清理任务 (`CleanupTasks`)
- **会话清理**: 过期用户会话清理
- **令牌清理**: 过期JWT令牌清理
- **验证码清理**: 过期验证码清理
- **临时文件清理**: 临时文件和缓存清理
- **日志归档**: 历史日志归档和清理

### 通知任务 (`NotificationTasks`)
- **异步邮件**: 异步邮件发送队列处理
- **异步短信**: 异步短信发送队列处理
- **批量通知**: 批量用户通知处理
- **失败重试**: 发送失败重试机制
- **状态更新**: 通知状态更新和统计

### 用户任务 (`UserTasks`)
- **数据同步**: 用户数据同步和更新
- **统计更新**: 用户统计数据更新
- **状态检查**: 用户状态定期检查
- **数据清理**: 用户相关数据清理
- **报表生成**: 用户相关报表生成

## 13. 系统监控

### 健康检查 (`routes/health.py`)
- **服务状态**: 服务运行状态检查
- **数据库连接**: PostgreSQL连接状态检查
- **缓存连接**: Redis连接状态检查
- **外部服务**: 邮件、短信服务状态检查
- **系统资源**: 内存、CPU使用率检查

### 系统监控 (`routes/system.py`)
- **性能监控**: API响应时间、吞吐量监控
- **错误监控**: 错误日志收集和分析
- **业务监控**: 业务指标统计和监控
- **资源监控**: 系统资源使用监控
- **告警机制**: 异常情况告警和通知

## 技术特性总结

### 安全特性
- 多层安全防护机制
- 完整的审计追踪
- 威胁检测和响应
- 数据加密和脱敏

### 性能特性
- 异步处理架构
- 智能缓存策略
- 数据库查询优化
- 分页和批量处理

### 可扩展性
- 微服务架构设计
- 依赖注入容器
- 模块化组件设计
- 插件化扩展机制

### 开发友好
- 完整的类型注解
- 自动生成API文档
- 统一的错误处理
- 丰富的测试用例
