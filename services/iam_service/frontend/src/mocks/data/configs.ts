/**
 * @file 系统配置Mock数据
 * @description 提供系统配置相关的模拟数据
 * @status 框架文件 - 完成
 */

import { SystemConfig } from '@/api/types'

export const mockConfigs: SystemConfig[] = [
  {
    id: 'config_1',
    key: 'app.name',
    value: 'ThingsMore',
    description: '应用程序名称',
    category: 'application',
    isPublic: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'config_2',
    key: 'app.version',
    value: '1.0.0',
    description: '应用程序版本号',
    category: 'application',
    isPublic: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'config_3',
    key: 'auth.session_timeout',
    value: 3600,
    description: '会话超时时间（秒）',
    category: 'security',
    isPublic: false,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'config_4',
    key: 'auth.max_login_attempts',
    value: 5,
    description: '最大登录尝试次数',
    category: 'security',
    isPublic: false,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'config_5',
    key: 'ui.theme',
    value: 'light',
    description: '默认UI主题',
    category: 'interface',
    isPublic: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'config_6',
    key: 'ui.language',
    value: 'zh-CN',
    description: '默认界面语言',
    category: 'interface',
    isPublic: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'config_7',
    key: 'notification.email_enabled',
    value: true,
    description: '启用邮件通知',
    category: 'notification',
    isPublic: false,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'config_8',
    key: 'notification.sms_enabled',
    value: false,
    description: '启用短信通知',
    category: 'notification',
    isPublic: false,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'config_9',
    key: 'system.log_level',
    value: 'INFO',
    description: '系统日志级别',
    category: 'system',
    isPublic: false,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'config_10',
    key: 'system.backup_enabled',
    value: true,
    description: '启用自动备份',
    category: 'system',
    isPublic: false,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
] 