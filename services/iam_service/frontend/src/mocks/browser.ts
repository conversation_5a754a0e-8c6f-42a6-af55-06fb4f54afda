/**
 * @file MSW浏览器端设置
 * @description 配置Mock Service Worker在浏览器中拦截API请求
 * @status 框架文件 - 完成
 */

import { setupWorker } from 'msw/browser'
import { handlers } from './handlers'

// 创建MSW worker
export const worker = setupWorker(...handlers)

// 启动worker的便捷函数
export async function startMockWorker() {
    if (import.meta.env.DEV) {
        try {
            await worker.start({
                onUnhandledRequest: 'bypass',
                serviceWorker: {
                    url: '/mockServiceWorker.js',
                },
            })
            console.log('[MSW] Mock Service Worker started')
        } catch (error) {
            console.error('[MSW] Failed to start Mock Service Worker:', error)
        }
    }
}