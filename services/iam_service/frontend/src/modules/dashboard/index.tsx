/**
 * @file 仪表板模块
 * @description 示例模块 - 仪表板功能
 * @status 框架示例 - 完成
 */

import React from 'react'
import { RouteObject } from 'react-router-dom'
import { Module, ModuleConfig } from '@/utils/ModuleLoader'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui'

// 仪表板组件
function DashboardPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
        <p className="text-gray-600">欢迎使用 ThingsMore 微服务系统</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>系统状态</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-600">运行正常</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>模块数量</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">3</div>
            <p className="text-sm text-gray-500">已加载模块</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>在线用户</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">12</div>
            <p className="text-sm text-gray-500">当前在线</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="text-2xl mb-2">📊</div>
              <div className="text-sm font-medium">数据分析</div>
            </button>
            <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="text-2xl mb-2">⚙️</div>
              <div className="text-sm font-medium">系统设置</div>
            </button>
            <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="text-2xl mb-2">👥</div>
              <div className="text-sm font-medium">用户管理</div>
            </button>
            <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="text-2xl mb-2">📝</div>
              <div className="text-sm font-medium">日志查看</div>
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// 模块配置
const config: ModuleConfig = {
  id: 'dashboard',
  name: '仪表板',
  version: '1.0.0',
  description: '系统仪表板和概览',
  enabled: true,
  lazy: false,
}

// 路由配置
const routes: RouteObject[] = [
  {
    path: '/dashboard',
    element: <DashboardPage />,
  },
]

// 模块定义
export const dashboardModule: Module = {
  config,
  component: DashboardPage,
  routes,
  initialize: async () => {
    console.log('Dashboard module initialized')
  },
  destroy: async () => {
    console.log('Dashboard module destroyed')
  },
}
