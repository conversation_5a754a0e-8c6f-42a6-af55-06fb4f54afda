/**
 * @file API类型定义
 * @description 定义与后端API交互的所有类型接口
 * @status 框架文件 - 完成
 */

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  code?: string
  timestamp?: string
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 错误响应类型
export interface ApiError {
  success: false
  message: string
  code?: string
  details?: Record<string, any>
  timestamp?: string
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  displayName?: string
  avatar?: string
  roles: string[]
  permissions: string[]
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  isActive: boolean
}

export interface CreateUserRequest {
  username: string
  email: string
  password: string
  displayName?: string
  roles?: string[]
}

export interface UpdateUserRequest {
  displayName?: string
  email?: string
  roles?: string[]
  isActive?: boolean
}

// 认证相关类型
export interface LoginRequest {
  username: string
  password: string
  rememberMe?: boolean
}

export interface LoginResponse {
  user: User
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface RefreshTokenRequest {
  refreshToken: string
}

// 系统配置类型
export interface SystemConfig {
  id: string
  key: string
  value: any
  description?: string
  category: string
  isPublic: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateConfigRequest {
  key: string
  value: any
  description?: string
  category: string
  isPublic?: boolean
}

export interface UpdateConfigRequest {
  value?: any
  description?: string
  isPublic?: boolean
}

// 通用查询参数
export interface QueryParams {
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  search?: string
  filters?: Record<string, any>
}

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// 请求配置类型
export interface RequestConfig {
  method?: HttpMethod
  headers?: Record<string, string>
  timeout?: number
  retries?: number
  params?: Record<string, any>
  data?: any
} 