/**
 * @file 全局样式文件
 * @description 包含Tailwind CSS指令和全局样式定义
 * @status 框架文件 - 完成
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
}

@layer components {
  /* 通用按钮样式 */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 shadow-sm;
  }
  
  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 border border-gray-300;
  }
  
  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50;
  }
  
  /* 通用卡片样式 */
  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm p-6;
  }
  
  /* 通用输入框样式 */
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
