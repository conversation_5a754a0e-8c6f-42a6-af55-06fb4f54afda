"""
权限服务使用示例

演示如何使用权限服务进行权限管理
"""

import asyncio
from datetime import datetime
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from services.iam_service.services.permission_service import PermissionService
from commonlib.storages.persistence.redis.repository import RedisRepository
from domain_common.models.iam_models import (
    User, Tenant, Role, Permission,
    UserRole, RolePermission, UserPolicy,
    PermissionPolicy, AuditLog
)


async def create_permission_service():
    """创建权限服务实例"""
    # 创建数据库引擎和会话
    engine = create_async_engine("sqlite+aiosqlite:///example.db")
    async_session = sessionmaker(engine, class_=AsyncSession)
    session = async_session()
    
    # 创建Redis仓库
    redis_repo = RedisRepository(host="localhost", port=6379, db=0)
    
    # 创建权限服务
    permission_service = PermissionService(
        session=session,
        redis_repo=redis_repo,
        user_model=User,
        tenant_model=Tenant,
        role_model=Role,
        permission_model=Permission,
        user_role_model=UserRole,
        role_permission_model=RolePermission,
        user_policy_model=UserPolicy,
        permission_policy_model=PermissionPolicy,
        audit_log_model=AuditLog
    )
    
    return permission_service, session


async def demo_permission_crud():
    """演示权限CRUD操作"""
    permission_service, session = await create_permission_service()
    
    try:
        print("=== 权限CRUD操作演示 ===")
        
        # 1. 创建权限
        print("\n1. 创建权限")
        permissions_to_create = [
            {
                "permission_name": "用户管理",
                "permission_code": "user:manage",
                "description": "用户管理权限",
                "resource_type": "user",
                "action": "manage",
                "tenant_id": "tenant_demo"
            },
            {
                "permission_name": "查看用户",
                "permission_code": "user:read",
                "description": "查看用户信息",
                "resource_type": "user",
                "action": "read",
                "tenant_id": "tenant_demo"
            },
            {
                "permission_name": "编辑用户",
                "permission_code": "user:write",
                "description": "编辑用户信息",
                "resource_type": "user",
                "action": "write",
                "tenant_id": "tenant_demo"
            }
        ]
        
        created_permissions = []
        for perm_data in permissions_to_create:
            perm_result = await permission_service.create_permission(**perm_data)
            created_permissions.append(perm_result)
            print(f"创建权限: {perm_result['permission_name']} ({perm_result['permission_code']})")
        
        # 2. 创建子权限
        print("\n2. 创建子权限")
        parent_permission_id = created_permissions[0]['permission_id']  # 用户管理权限作为父权限
        
        child_permission = await permission_service.create_permission(
            permission_name="删除用户",
            permission_code="user:delete",
            description="删除用户信息",
            resource_type="user",
            action="delete",
            tenant_id="tenant_demo",
            parent_id=parent_permission_id
        )
        print(f"创建子权限: {child_permission['permission_name']} (父权限: {parent_permission_id})")
        
        # 3. 查询权限列表
        print("\n3. 查询权限列表")
        list_result = await permission_service.list_permissions(
            tenant_id="tenant_demo",
            search="用户",
            limit=10
        )
        print(f"查询到 {list_result['total']} 个权限")
        for perm in list_result['permissions']:
            print(f"  - {perm['permission_name']} ({perm['permission_code']}) - 层级: {perm['level']}")
        
        # 4. 获取权限详情
        print("\n4. 获取权限详情")
        detail_result = await permission_service.get_permission_detail(
            permission_id=parent_permission_id,
            tenant_id="tenant_demo"
        )
        print(f"权限详情: {detail_result['permission_name']}")
        print(f"子权限数量: {len(detail_result['children'])}")
        for child in detail_result['children']:
            print(f"  - 子权限: {child['permission_name']}")
        
        # 5. 更新权限
        print("\n5. 更新权限")
        update_result = await permission_service.update_permission(
            permission_id=created_permissions[1]['permission_id'],
            tenant_id="tenant_demo",
            description="更新后的查看用户权限描述"
        )
        print(f"更新权限: {update_result['permission_name']}")
        
    except Exception as e:
        print(f"权限CRUD演示出错: {e}")
        await session.rollback()
    finally:
        await session.close()


async def demo_permission_tree():
    """演示权限树功能"""
    permission_service, session = await create_permission_service()
    
    try:
        print("\n=== 权限树功能演示 ===")
        
        # 1. 获取完整权限树
        print("\n1. 获取完整权限树")
        tree_result = await permission_service.get_permission_tree(
            tenant_id="tenant_demo"
        )
        
        print(f"权限树包含 {tree_result['total']} 个权限")
        
        def print_tree(permissions, indent=0):
            """递归打印权限树"""
            for perm in permissions:
                prefix = "  " * indent + "├─ " if indent > 0 else ""
                print(f"{prefix}{perm['permission_name']} ({perm['permission_code']})")
                if perm['children']:
                    print_tree(perm['children'], indent + 1)
        
        print_tree(tree_result['tree'])
        
        # 2. 按资源类型过滤权限树
        print("\n2. 按资源类型过滤权限树")
        filtered_tree = await permission_service.get_permission_tree(
            tenant_id="tenant_demo",
            resource_type="user"
        )
        
        print(f"用户相关权限树包含 {filtered_tree['total']} 个权限")
        print_tree(filtered_tree['tree'])
        
    except Exception as e:
        print(f"权限树演示出错: {e}")
        await session.rollback()
    finally:
        await session.close()


async def demo_permission_management():
    """演示权限管理功能"""
    permission_service, session = await create_permission_service()
    
    try:
        print("\n=== 权限管理功能演示 ===")
        
        # 1. 批量创建权限
        print("\n1. 批量创建权限")
        batch_permissions = [
            ("角色管理", "role:manage", "角色管理权限", "role", "manage"),
            ("查看角色", "role:read", "查看角色信息", "role", "read"),
            ("创建角色", "role:create", "创建新角色", "role", "create"),
            ("编辑角色", "role:update", "编辑角色信息", "role", "update"),
            ("删除角色", "role:delete", "删除角色", "role", "delete"),
        ]
        
        role_permissions = []
        for name, code, desc, resource, action in batch_permissions:
            perm = await permission_service.create_permission(
                permission_name=name,
                permission_code=code,
                description=desc,
                resource_type=resource,
                action=action,
                tenant_id="tenant_demo"
            )
            role_permissions.append(perm)
            print(f"创建权限: {name}")
        
        # 2. 建立权限层级关系
        print("\n2. 建立权限层级关系")
        parent_role_perm = role_permissions[0]  # 角色管理作为父权限
        
        for child_perm in role_permissions[1:]:
            # 更新子权限的父权限关系（这里简化演示，实际需要在创建时指定）
            print(f"  {child_perm['permission_name']} -> {parent_role_perm['permission_name']}")
        
        # 3. 权限搜索和过滤
        print("\n3. 权限搜索和过滤")
        
        # 搜索包含"角色"的权限
        search_result = await permission_service.list_permissions(
            tenant_id="tenant_demo",
            search="角色",
            limit=20
        )
        print(f"搜索'角色'相关权限: {search_result['total']} 个")
        
        # 按资源类型过滤
        filter_result = await permission_service.list_permissions(
            tenant_id="tenant_demo",
            resource_type="role",
            limit=20
        )
        print(f"角色资源相关权限: {filter_result['total']} 个")
        
        # 4. 权限状态管理
        print("\n4. 权限状态管理")
        
        # 禁用一个权限
        disable_perm = role_permissions[-1]  # 删除角色权限
        await permission_service.update_permission(
            permission_id=disable_perm['permission_id'],
            tenant_id="tenant_demo",
            status="inactive"
        )
        print(f"禁用权限: {disable_perm['permission_name']}")
        
        # 5. 权限删除演示
        print("\n5. 权限删除演示")
        
        # 创建一个临时权限用于删除演示
        temp_perm = await permission_service.create_permission(
            permission_name="临时权限",
            permission_code="temp:test",
            description="用于删除演示的临时权限",
            resource_type="temp",
            action="test",
            tenant_id="tenant_demo"
        )
        
        # 删除临时权限
        delete_result = await permission_service.delete_permission(
            permission_id=temp_perm['permission_id'],
            tenant_id="tenant_demo",
            force=False
        )
        print(f"删除权限: {temp_perm['permission_name']} - 成功: {delete_result['success']}")
        
    except Exception as e:
        print(f"权限管理演示出错: {e}")
        await session.rollback()
    finally:
        await session.close()


async def main():
    """主函数"""
    print("权限服务使用示例")
    print("=" * 50)
    
    # 运行各个演示
    await demo_permission_crud()
    await demo_permission_tree()
    await demo_permission_management()
    
    print("\n演示完成！")


if __name__ == "__main__":
    asyncio.run(main())
