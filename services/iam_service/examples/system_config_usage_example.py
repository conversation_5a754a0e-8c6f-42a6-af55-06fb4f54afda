"""
系统配置服务使用示例

演示如何使用系统配置服务进行配置管理
"""

import asyncio
from datetime import datetime
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from services.iam_service.services.system_config_service import SystemConfigService
from commonlib.storages.persistence.redis.repository import RedisRepository
from domain_common.models.iam_models import (
    Tenant, SystemConfig, TenantConfig, AuditLog
)


async def create_system_config_service():
    """创建系统配置服务实例"""
    # 创建数据库引擎和会话
    engine = create_async_engine("sqlite+aiosqlite:///example.db")
    async_session = sessionmaker(engine, class_=AsyncSession)
    session = async_session()
    
    # 创建Redis仓库
    redis_repo = RedisRepository(host="localhost", port=6379, db=0)
    
    # 创建系统配置服务
    system_config_service = SystemConfigService(
        session=session,
        redis_repo=redis_repo,
        tenant_model=Tenant,
        system_config_model=SystemConfig,
        tenant_config_model=TenantConfig,
        audit_log_model=AuditLog,
        encryption_key="your_32_byte_encryption_key_here"
    )
    
    return system_config_service, session


async def demo_global_config_management():
    """演示全局配置管理"""
    system_config_service, session = await create_system_config_service()
    
    try:
        print("=== 全局配置管理演示 ===")
        
        # 1. 设置全局配置
        print("\n1. 设置全局配置")
        set_result = await system_config_service.set_config(
            tenant_id=None,
            config_key="system.max_upload_size",
            config_value="100MB",
            description="系统最大文件上传大小",
            is_sensitive=False,
            category="general"
        )
        
        print(f"配置设置成功:")
        print(f"  配置键: {set_result['config_key']}")
        print(f"  新值: {set_result['new_value']}")
        print(f"  配置ID: {set_result['config_id']}")
        
        # 2. 获取全局配置
        print("\n2. 获取全局配置")
        get_result = await system_config_service.get_config(
            tenant_id=None,
            config_key="system.max_upload_size"
        )
        
        print(f"配置获取成功:")
        print(f"  配置键: {get_result['config_key']}")
        print(f"  配置值: {get_result['config_value']}")
        print(f"  来源: {get_result['source']}")
        print(f"  是否继承: {get_result['is_inherited']}")
        
        # 3. 设置敏感配置
        print("\n3. 设置敏感配置")
        sensitive_result = await system_config_service.set_config(
            tenant_id=None,
            config_key="security.jwt_secret",
            config_value="super_secret_jwt_key_12345",
            description="JWT签名密钥",
            is_sensitive=True,
            category="security"
        )
        
        print(f"敏感配置设置成功:")
        print(f"  配置键: {sensitive_result['config_key']}")
        print(f"  新值: [敏感配置已加密]")
        
        # 4. 获取敏感配置（不解密）
        print("\n4. 获取敏感配置（不解密）")
        sensitive_get = await system_config_service.get_config(
            tenant_id=None,
            config_key="security.jwt_secret",
            decrypt_sensitive=False
        )
        
        print(f"敏感配置获取:")
        print(f"  配置值: {sensitive_get['config_value']}")  # 应该显示 [敏感配置]
        
        # 5. 获取敏感配置（解密）
        print("\n5. 获取敏感配置（解密）")
        sensitive_decrypt = await system_config_service.get_config(
            tenant_id=None,
            config_key="security.jwt_secret",
            decrypt_sensitive=True
        )
        
        print(f"敏感配置解密:")
        print(f"  配置值: {sensitive_decrypt['config_value']}")
        
    except Exception as e:
        print(f"全局配置管理演示出错: {e}")
    finally:
        await session.close()


async def demo_tenant_config_management():
    """演示租户配置管理"""
    system_config_service, session = await create_system_config_service()
    
    try:
        print("\n=== 租户配置管理演示 ===")
        
        # 1. 设置租户配置
        print("\n1. 设置租户配置")
        tenant_result = await system_config_service.set_config(
            tenant_id="tenant_demo",
            config_key="ui.theme",
            config_value="dark",
            description="用户界面主题",
            is_sensitive=False,
            category="general"
        )
        
        print(f"租户配置设置成功:")
        print(f"  配置键: {tenant_result['config_key']}")
        print(f"  配置值: {tenant_result['new_value']}")
        
        # 2. 设置租户特定的上传限制
        print("\n2. 设置租户特定的上传限制")
        tenant_upload_result = await system_config_service.set_config(
            tenant_id="tenant_demo",
            config_key="system.max_upload_size",
            config_value="50MB",  # 比全局配置更严格
            description="租户特定的上传大小限制",
            is_sensitive=False,
            category="general"
        )
        
        print(f"租户上传限制设置成功:")
        print(f"  配置值: {tenant_upload_result['new_value']}")
        
        # 3. 获取租户配置（优先级：租户 > 全局 > 默认）
        print("\n3. 获取租户配置（带继承）")
        tenant_get = await system_config_service.get_config(
            tenant_id="tenant_demo",
            config_key="system.max_upload_size",
            include_inherited=True
        )
        
        print(f"租户配置获取:")
        print(f"  配置值: {tenant_get['config_value']}")  # 应该是 50MB
        print(f"  来源: {tenant_get['source']}")  # 应该是 tenant
        
        # 4. 获取不存在的租户配置（回退到全局）
        print("\n4. 获取不存在的租户配置（回退到全局）")
        fallback_get = await system_config_service.get_config(
            tenant_id="tenant_demo",
            config_key="security.jwt_secret",
            include_inherited=True,
            decrypt_sensitive=False
        )
        
        print(f"回退到全局配置:")
        print(f"  配置值: {fallback_get['config_value']}")
        print(f"  来源: {fallback_get['source']}")  # 应该是 global
        print(f"  是否继承: {fallback_get['is_inherited']}")  # 应该是 True
        
        # 5. 获取默认配置
        print("\n5. 获取默认配置")
        default_get = await system_config_service.get_config(
            tenant_id="tenant_demo",
            config_key="password_policy.min_length",
            include_inherited=True
        )
        
        print(f"默认配置获取:")
        print(f"  配置值: {default_get['config_value']}")  # 应该是 8
        print(f"  来源: {default_get['source']}")  # 应该是 default
        
    except Exception as e:
        print(f"租户配置管理演示出错: {e}")
    finally:
        await session.close()


async def demo_config_query_and_batch():
    """演示配置查询和批量操作"""
    system_config_service, session = await create_system_config_service()
    
    try:
        print("\n=== 配置查询和批量操作演示 ===")
        
        # 1. 查询配置列表
        print("\n1. 查询租户配置列表")
        query_result = await system_config_service.query_configs(
            tenant_id="tenant_demo",
            category="general",
            include_inherited=True,
            page=1,
            page_size=10
        )
        
        print(f"配置查询结果:")
        print(f"  总数: {query_result['total']}")
        print(f"  当前页: {query_result['page']}")
        print(f"  是否有下一页: {query_result['has_next']}")
        
        print("  配置列表:")
        for config in query_result['configs']:
            print(f"    {config['config_key']}: {config['config_value']} ({config['source'] if config.get('source') else 'tenant'})")
        
        # 2. 按关键词搜索配置
        print("\n2. 按关键词搜索配置")
        search_result = await system_config_service.query_configs(
            tenant_id="tenant_demo",
            keyword="upload",
            include_inherited=True,
            page=1,
            page_size=5
        )
        
        print(f"搜索结果:")
        for config in search_result['configs']:
            print(f"  {config['config_key']}: {config['config_value']}")
        
        # 3. 批量设置配置
        print("\n3. 批量设置配置")
        batch_configs = [
            {
                "config_key": "notification.email_enabled",
                "config_value": True,
                "description": "启用邮件通知",
                "category": "notification"
            },
            {
                "config_key": "notification.sms_enabled",
                "config_value": False,
                "description": "启用短信通知",
                "category": "notification"
            },
            {
                "config_key": "feature.advanced_search",
                "config_value": True,
                "description": "启用高级搜索功能",
                "category": "feature"
            }
        ]
        
        batch_result = await system_config_service.batch_set_configs(
            tenant_id="tenant_demo",
            configs=batch_configs
        )
        
        print(f"批量设置结果:")
        print(f"  成功数量: {batch_result['success_count']}")
        print(f"  失败数量: {batch_result['failed_count']}")
        if batch_result['failed_configs']:
            print(f"  失败配置: {batch_result['failed_configs']}")
        
        # 4. 查询特定分类的配置
        print("\n4. 查询通知分类的配置")
        notification_configs = await system_config_service.query_configs(
            tenant_id="tenant_demo",
            category="notification",
            page=1,
            page_size=10
        )
        
        print(f"通知配置:")
        for config in notification_configs['configs']:
            print(f"  {config['config_key']}: {config['config_value']}")
        
    except Exception as e:
        print(f"配置查询和批量操作演示出错: {e}")
    finally:
        await session.close()


async def demo_config_deletion_and_reset():
    """演示配置删除和重置"""
    system_config_service, session = await create_system_config_service()
    
    try:
        print("\n=== 配置删除和重置演示 ===")
        
        # 1. 删除单个配置
        print("\n1. 删除单个配置")
        try:
            delete_result = await system_config_service.delete_config(
                tenant_id="tenant_demo",
                config_key="ui.theme"
            )
            
            print(f"配置删除成功:")
            print(f"  配置键: {delete_result['config_key']}")
            print(f"  删除时间: {delete_result['deleted_at']}")
        except Exception as e:
            print(f"删除配置失败: {e}")
        
        # 2. 尝试删除系统核心配置（应该失败）
        print("\n2. 尝试删除系统核心配置")
        try:
            await system_config_service.delete_config(
                tenant_id=None,
                config_key="password_policy.min_length"
            )
        except Exception as e:
            print(f"删除系统核心配置失败（预期）: {e}")
        
        # 3. 重置特定分类的配置
        print("\n3. 重置通知分类的配置")
        reset_result = await system_config_service.reset_configs(
            tenant_id="tenant_demo",
            category="notification"
        )
        
        print(f"配置重置结果:")
        print(f"  重置数量: {reset_result['reset_count']}")
        print(f"  重置分类: {reset_result['category']}")
        print(f"  重置时间: {reset_result['reset_at']}")
        
        # 4. 验证重置后的配置
        print("\n4. 验证重置后的配置")
        after_reset = await system_config_service.query_configs(
            tenant_id="tenant_demo",
            category="notification",
            page=1,
            page_size=10
        )
        
        print(f"重置后的通知配置数量: {after_reset['total']}")
        
    except Exception as e:
        print(f"配置删除和重置演示出错: {e}")
    finally:
        await session.close()


async def demo_config_inheritance():
    """演示配置继承机制"""
    system_config_service, session = await create_system_config_service()
    
    try:
        print("\n=== 配置继承机制演示 ===")
        
        # 1. 设置全局配置
        print("\n1. 设置全局配置")
        await system_config_service.set_config(
            tenant_id=None,
            config_key="api.rate_limit",
            config_value=1000,
            description="API速率限制（每分钟请求数）",
            category="general"
        )
        print("全局API速率限制设置为: 1000")
        
        # 2. 租户A使用默认配置
        print("\n2. 租户A使用默认配置")
        tenant_a_config = await system_config_service.get_config(
            tenant_id="tenant_a",
            config_key="api.rate_limit",
            include_inherited=True
        )
        
        print(f"租户A的API速率限制: {tenant_a_config['config_value']} (来源: {tenant_a_config['source']})")
        
        # 3. 租户B设置自定义配置
        print("\n3. 租户B设置自定义配置")
        await system_config_service.set_config(
            tenant_id="tenant_b",
            config_key="api.rate_limit",
            config_value=2000,
            description="租户B的API速率限制",
            category="general"
        )
        
        tenant_b_config = await system_config_service.get_config(
            tenant_id="tenant_b",
            config_key="api.rate_limit",
            include_inherited=True
        )
        
        print(f"租户B的API速率限制: {tenant_b_config['config_value']} (来源: {tenant_b_config['source']})")
        
        # 4. 展示继承层次
        print("\n4. 配置继承层次演示")
        print("配置优先级: 租户配置 > 全局配置 > 默认配置")
        
        # 测试不存在的配置键（使用默认值）
        default_config = await system_config_service.get_config(
            tenant_id="tenant_a",
            config_key="password_policy.min_length",
            include_inherited=True
        )
        
        print(f"密码最小长度 (默认): {default_config['config_value']} (来源: {default_config['source']})")
        
    except Exception as e:
        print(f"配置继承机制演示出错: {e}")
    finally:
        await session.close()


async def main():
    """主函数"""
    print("系统配置服务使用示例")
    print("=" * 50)
    
    # 运行各个演示
    await demo_global_config_management()
    await demo_tenant_config_management()
    await demo_config_query_and_batch()
    await demo_config_deletion_and_reset()
    await demo_config_inheritance()
    
    print("\n演示完成！")


if __name__ == "__main__":
    asyncio.run(main())
