"""
认证服务使用示例

演示如何使用认证服务进行登录、登出、密码管理、MFA等功能
"""

import asyncio
from datetime import datetime
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from services.iam_service.services.auth_service import AuthService
from commonlib.storages.persistence.redis.repository import RedisRepository
from domain_common.models.iam_models import (
    User, Tenant, Role, Permission, UserRole, RolePermission,
    UserSessionHistory, UserMFA, VerificationCode, PasswordHistory, AuditLog
)


async def create_auth_service():
    """创建认证服务实例"""
    # 创建数据库引擎和会话
    engine = create_async_engine("sqlite+aiosqlite:///example.db")
    async_session = sessionmaker(engine, class_=AsyncSession)
    session = async_session()
    
    # 创建Redis仓库
    redis_repo = RedisRepository(host="localhost", port=6379, db=0)
    
    # 创建认证服务
    auth_service = AuthService(
        session=session,
        redis_repo=redis_repo,
        user_model=User,
        tenant_model=Tenant,
        role_model=Role,
        permission_model=Permission,
        user_role_model=UserRole,
        role_permission_model=RolePermission,
        user_session_history_model=UserSessionHistory,
        user_mfa_model=UserMFA,
        verification_code_model=VerificationCode,
        password_history_model=PasswordHistory,
        audit_log_model=AuditLog
    )
    
    return auth_service, session


async def demo_user_login():
    """演示用户登录功能"""
    auth_service, session = await create_auth_service()
    
    try:
        print("=== 用户登录演示 ===")
        
        # 1. 用户名密码登录
        print("\n1. 用户名密码登录")
        login_result = await auth_service.login(
            tenant_id="tenant_demo",
            login_type="username",
            identifier="demo_user",
            credential="password123",
            remember_me=False,
            device_info={
                "device_name": "iPhone 15",
                "os": "iOS 17.0",
                "ip_address": "*************",
                "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)"
            }
        )
        
        print(f"登录成功！")
        print(f"访问令牌: {login_result['access_token'][:50]}...")
        print(f"会话ID: {login_result['session_id']}")
        print(f"用户信息: {login_result['user_info']['username']}")
        print(f"令牌过期时间: {login_result['expires_in']}秒")
        
        # 2. 邮箱登录
        print("\n2. 邮箱登录")
        email_login_result = await auth_service.login(
            tenant_id="tenant_demo",
            login_type="email",
            identifier="<EMAIL>",
            credential="password123",
            remember_me=True  # 记住登录状态
        )
        print(f"邮箱登录成功！会话ID: {email_login_result['session_id']}")
        
        # 3. 手机号登录
        print("\n3. 手机号登录")
        phone_login_result = await auth_service.login(
            tenant_id="tenant_demo",
            login_type="phone",
            identifier="13800138000",
            credential="password123",
            remember_me=False
        )
        print(f"手机号登录成功！会话ID: {phone_login_result['session_id']}")
        
        # 4. MFA登录（如果用户启用了MFA）
        print("\n4. MFA登录演示")
        try:
            mfa_login_result = await auth_service.login(
                tenant_id="tenant_demo",
                login_type="username",
                identifier="mfa_user",
                credential="password123",
                mfa_code="123456"  # TOTP验证码
            )
            print(f"MFA登录成功！会话ID: {mfa_login_result['session_id']}")
        except Exception as e:
            print(f"MFA登录失败（预期）: {e}")
        
    except Exception as e:
        print(f"登录演示出错: {e}")
        await session.rollback()
    finally:
        await session.close()


async def demo_session_management():
    """演示会话管理功能"""
    auth_service, session = await create_auth_service()
    
    try:
        print("\n=== 会话管理演示 ===")
        
        # 1. 查看用户会话
        print("\n1. 查看用户会话")
        sessions_result = await auth_service.list_sessions(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            include_expired=False
        )
        
        print(f"活跃会话数: {sessions_result['total_sessions']}")
        for session_info in sessions_result['active_sessions']:
            print(f"  会话ID: {session_info['session_id']}")
            print(f"  设备: {session_info['device_info']['device_name']}")
            print(f"  IP地址: {session_info['device_info']['ip_address']}")
            print(f"  最后活动: {session_info['last_activity']}")
            print(f"  是否当前会话: {session_info['is_current']}")
            print()
        
        # 2. 强制下线会话
        print("\n2. 强制下线会话")
        if sessions_result['active_sessions']:
            session_to_terminate = sessions_result['active_sessions'][0]['session_id']
            terminate_result = await auth_service.terminate_session(
                tenant_id="tenant_demo",
                user_id="user_demo_123",
                session_id=session_to_terminate,
                reason="安全原因"
            )
            print(f"会话 {terminate_result['session_id']} 已强制下线")
            print(f"下线时间: {terminate_result['terminated_at']}")
            print(f"下线原因: {terminate_result['reason']}")
        
        # 3. 用户登出
        print("\n3. 用户登出")
        logout_result = await auth_service.logout(
            tenant_id="tenant_demo",
            session_id="session_demo_456",
            logout_all_devices=False
        )
        print(f"用户 {logout_result['user_id']} 登出成功")
        print(f"登出时间: {logout_result['logout_time']}")
        print(f"登出会话数: {logout_result['logged_out_sessions']}")
        print(f"剩余会话数: {logout_result['remaining_sessions']}")
        
        # 4. 全设备登出
        print("\n4. 全设备登出")
        logout_all_result = await auth_service.logout(
            tenant_id="tenant_demo",
            session_id="session_demo_789",
            logout_all_devices=True
        )
        print(f"全设备登出成功，共登出 {logout_all_result['logged_out_sessions']} 个会话")
        
    except Exception as e:
        print(f"会话管理演示出错: {e}")
    finally:
        await session.close()


async def demo_password_management():
    """演示密码管理功能"""
    auth_service, session = await create_auth_service()
    
    try:
        print("\n=== 密码管理演示 ===")
        
        # 1. 修改密码
        print("\n1. 修改密码")
        change_result = await auth_service.change_password(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            old_password="old_password123",
            new_password="new_password456",
            logout_other_sessions=True
        )
        print(f"密码修改成功！")
        print(f"修改时间: {change_result['password_changed_at']}")
        print(f"新密码过期时间: {change_result['password_expires_at']}")
        print(f"终止的会话数: {change_result['sessions_terminated']}")
        
        # 2. 忘记密码
        print("\n2. 忘记密码")
        forgot_result = await auth_service.forgot_password(
            tenant_id="tenant_demo",
            identifier="<EMAIL>",
            identifier_type="email",
            captcha_token="captcha_token_123"
        )
        print(f"重置密码邮件已发送")
        print(f"重置令牌ID: {forgot_result['reset_token_id']}")
        print(f"过期时间: {forgot_result['expires_in']}秒")
        print(f"发送到: {forgot_result['sent_to']}")
        
        # 3. 重置密码
        print("\n3. 重置密码")
        reset_result = await auth_service.reset_password(
            reset_token="reset_token_demo_123",
            new_password="reset_password789",
            confirm_password="reset_password789"
        )
        print(f"密码重置成功！")
        print(f"重置时间: {reset_result['password_reset_at']}")
        print(f"所有会话已终止: {reset_result['all_sessions_terminated']}")
        
    except Exception as e:
        print(f"密码管理演示出错: {e}")
    finally:
        await session.close()


async def demo_mfa_management():
    """演示MFA管理功能"""
    auth_service, session = await create_auth_service()
    
    try:
        print("\n=== MFA管理演示 ===")
        
        # 1. 设置TOTP MFA
        print("\n1. 设置TOTP MFA")
        setup_result = await auth_service.setup_mfa(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            mfa_type="totp",
            device_name="iPhone 15"
        )
        print(f"MFA设置信息生成成功！")
        print(f"密钥: {setup_result['secret_key']}")
        print(f"二维码URL: {setup_result['qr_code_url']}")
        print(f"设置令牌: {setup_result['setup_token']}")
        print(f"备用恢复码数量: {len(setup_result['backup_codes'])}")
        
        # 2. 验证并启用MFA
        print("\n2. 验证并启用MFA")
        verify_result = await auth_service.verify_mfa(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            setup_token=setup_result['setup_token'],
            verification_code="123456"  # 用户从TOTP应用获取的验证码
        )
        print(f"MFA启用成功！")
        print(f"用户ID: {verify_result['user_id']}")
        print(f"MFA状态: {verify_result['mfa_enabled']}")
        print(f"启用时间: {verify_result['enabled_at']}")
        
        # 3. 禁用MFA
        print("\n3. 禁用MFA")
        disable_result = await auth_service.disable_mfa(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            password="user_password123",
            mfa_code="654321",  # 当前MFA验证码
            reason="用户更换设备"
        )
        print(f"MFA禁用成功！")
        print(f"用户ID: {disable_result['user_id']}")
        print(f"MFA状态: {disable_result['mfa_enabled']}")
        print(f"禁用时间: {disable_result['disabled_at']}")
        print(f"禁用原因: {disable_result['reason']}")
        
    except Exception as e:
        print(f"MFA管理演示出错: {e}")
    finally:
        await session.close()


async def demo_verification_and_security():
    """演示验证码和安全功能"""
    auth_service, session = await create_auth_service()
    
    try:
        print("\n=== 验证码和安全功能演示 ===")
        
        # 1. 验证验证码
        print("\n1. 验证验证码")
        verify_code_result = await auth_service.verify_code(
            code_id="code_demo_123",
            verification_code="123456",
            code_type="sms"
        )
        print(f"验证码验证成功！")
        print(f"验证码ID: {verify_code_result['code_id']}")
        print(f"验证结果: {verify_code_result['verified']}")
        print(f"验证时间: {verify_code_result['verified_at']}")
        
        # 2. 敏感操作确认
        print("\n2. 敏感操作确认")
        confirm_result = await auth_service.confirm_operation(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            operation_type="delete_tenant",
            operation_data={"tenant_id": "tenant_to_delete"},
            confirmation_method="sms",
            verification_code="789012"
        )
        print(f"敏感操作确认成功！")
        print(f"操作令牌: {confirm_result['operation_token']}")
        print(f"令牌过期时间: {confirm_result['expires_in']}秒")
        print(f"确认时间: {confirm_result['confirmed_at']}")
        
        # 3. 刷新令牌
        print("\n3. 刷新令牌")
        refresh_result = await auth_service.refresh_token(
            tenant_id="tenant_demo",
            refresh_token="refresh_token_demo_123",
            session_id="session_demo_456"
        )
        print(f"令牌刷新成功！")
        print(f"新访问令牌: {refresh_result['access_token'][:50]}...")
        print(f"新刷新令牌: {refresh_result['refresh_token'][:50]}...")
        print(f"过期时间: {refresh_result['expires_in']}秒")
        print(f"签发时间: {refresh_result['issued_at']}")
        
    except Exception as e:
        print(f"验证码和安全功能演示出错: {e}")
    finally:
        await session.close()


async def main():
    """主函数"""
    print("认证服务使用示例")
    print("=" * 50)
    
    # 运行各个演示
    await demo_user_login()
    await demo_session_management()
    await demo_password_management()
    await demo_mfa_management()
    await demo_verification_and_security()
    
    print("\n演示完成！")


if __name__ == "__main__":
    asyncio.run(main())
