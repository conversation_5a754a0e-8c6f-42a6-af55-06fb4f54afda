"""
系统服务使用示例

演示如何使用系统服务进行指标查询、日志管理、配置管理等功能
"""

import asyncio
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from services.iam_service.services.system_service import SystemService
from commonlib.storages.persistence.redis.repository import RedisRepository
from domain_common.models.iam_models import (
    User, Tenant, AuditLog, SystemConfig, CacheKey,
    SecurityPolicy, SecurityEvent
)


async def create_system_service():
    """创建系统服务实例"""
    # 创建数据库引擎和会话
    engine = create_async_engine("sqlite+aiosqlite:///example.db")
    async_session = sessionmaker(engine, class_=AsyncSession)
    session = async_session()
    
    # 创建Redis仓库
    redis_repo = RedisRepository(host="localhost", port=6379, db=0)
    
    # 创建系统服务
    system_service = SystemService(
        session=session,
        redis_repo=redis_repo,
        user_model=User,
        tenant_model=Tenant,
        audit_log_model=AuditLog,
        system_config_model=SystemConfig,
        cache_key_model=CacheKey,
        security_policy_model=SecurityPolicy,
        security_event_model=SecurityEvent
    )
    
    return system_service, session


async def demo_metrics_query():
    """演示指标查询功能"""
    system_service, session = await create_system_service()
    
    try:
        print("=== 系统指标查询演示 ===")
        
        # 1. 查询系统级指标
        print("\n1. 查询系统级指标（最近1小时）")
        system_metrics = await system_service.query_metrics(
            time_range="1h",
            metric_types=["system", "database", "redis"]
        )
        
        print(f"查询时间: {system_metrics['timestamp']}")
        print(f"时间范围: {system_metrics['time_range']['duration']}")
        
        # 显示系统指标
        sys_metrics = system_metrics["system_metrics"]
        print(f"CPU使用率: {sys_metrics['cpu_usage']}%")
        print(f"内存使用率: {sys_metrics['memory_usage']}%")
        print(f"磁盘使用率: {sys_metrics['disk_usage']}%")
        print(f"活跃会话数: {sys_metrics['active_sessions']}")
        print(f"每分钟API请求数: {sys_metrics['api_requests_per_minute']}")
        print(f"数据库连接数: {sys_metrics['database_connections']}")
        print(f"Redis内存使用率: {sys_metrics['redis_memory_usage']}%")
        print(f"错误率: {sys_metrics['error_rate']}%")
        print(f"平均响应时间: {sys_metrics['response_time_avg']}ms")
        
        # 2. 查询租户指标
        print("\n2. 查询特定租户指标")
        tenant_metrics = await system_service.query_metrics(
            tenant_id="tenant_demo",
            time_range="24h",
            metric_types=["tenants"]
        )
        
        if tenant_metrics.get("tenant_metrics"):
            for tenant_metric in tenant_metrics["tenant_metrics"]:
                print(f"租户ID: {tenant_metric['tenant_id']}")
                print(f"用户数量: {tenant_metric['user_count']}")
                print(f"活跃用户数: {tenant_metric['active_users']}")
                print(f"知识库数量: {tenant_metric['knowledge_base_count']}")
                print(f"文档数量: {tenant_metric['document_count']}")
                print(f"今日API调用: {tenant_metric['api_calls_today']}")
                print(f"存储使用量: {tenant_metric['storage_used']} bytes")
        
        # 3. 查询所有租户指标
        print("\n3. 查询所有租户指标")
        all_tenant_metrics = await system_service.query_metrics(
            time_range="7d",
            metric_types=["tenants"]
        )
        
        if all_tenant_metrics.get("tenant_metrics"):
            print(f"总租户数: {len(all_tenant_metrics['tenant_metrics'])}")
            total_users = sum(tm['user_count'] for tm in all_tenant_metrics['tenant_metrics'])
            total_active = sum(tm['active_users'] for tm in all_tenant_metrics['tenant_metrics'])
            print(f"总用户数: {total_users}")
            print(f"总活跃用户数: {total_active}")
        
    except Exception as e:
        print(f"指标查询演示出错: {e}")
    finally:
        await session.close()


async def demo_logs_query():
    """演示日志查询功能"""
    system_service, session = await create_system_service()
    
    try:
        print("\n=== 系统日志查询演示 ===")
        
        # 1. 查询所有日志
        print("\n1. 查询最近的系统日志")
        logs_result = await system_service.query_logs(
            limit=10
        )
        
        print(f"日志总数: {logs_result['total']}")
        print(f"是否有更多: {logs_result['has_more']}")
        
        for log in logs_result['logs']:
            print(f"[{log['timestamp']}] {log['level']} - {log['service']}: {log['message']}")
            if log.get('metadata'):
                print(f"  元数据: {log['metadata']}")
        
        # 2. 按级别过滤日志
        print("\n2. 查询ERROR级别日志")
        error_logs = await system_service.query_logs(
            level="ERROR",
            limit=5
        )
        
        print(f"错误日志数量: {error_logs['total']}")
        for log in error_logs['logs']:
            print(f"[{log['timestamp']}] {log['service']}: {log['message']}")
        
        # 3. 按服务过滤日志
        print("\n3. 查询IAM服务日志")
        iam_logs = await system_service.query_logs(
            service="iam_service",
            limit=5
        )
        
        print(f"IAM服务日志数量: {iam_logs['total']}")
        for log in iam_logs['logs']:
            print(f"[{log['timestamp']}] {log['level']}: {log['message']}")
        
        # 4. 按租户过滤日志
        print("\n4. 查询特定租户日志")
        tenant_logs = await system_service.query_logs(
            tenant_id="tenant_demo",
            limit=5
        )
        
        print(f"租户日志数量: {tenant_logs['total']}")
        for log in tenant_logs['logs']:
            print(f"[{log['timestamp']}] {log['service']}: {log['message']}")
        
    except Exception as e:
        print(f"日志查询演示出错: {e}")
    finally:
        await session.close()


async def demo_audit_logs_query():
    """演示审计日志查询功能"""
    system_service, session = await create_system_service()
    
    try:
        print("\n=== 审计日志查询演示 ===")
        
        # 1. 查询租户审计日志
        print("\n1. 查询租户审计日志")
        audit_logs = await system_service.query_audit_logs(
            tenant_id="tenant_demo",
            limit=10
        )
        
        print(f"审计日志总数: {audit_logs['total']}")
        print(f"是否有更多: {audit_logs['has_more']}")
        
        for log in audit_logs['audit_logs']:
            print(f"[{log['timestamp']}] {log['username'] or '系统'} - {log['action']}")
            print(f"  资源类型: {log['resource_type']}")
            print(f"  IP地址: {log['ip_address']}")
            if log['details']:
                print(f"  详细信息: {log['details']}")
        
        # 2. 按用户过滤审计日志
        print("\n2. 查询特定用户审计日志")
        user_audit_logs = await system_service.query_audit_logs(
            tenant_id="tenant_demo",
            user_id="user_demo_123",
            limit=5
        )
        
        print(f"用户审计日志数量: {user_audit_logs['total']}")
        for log in user_audit_logs['audit_logs']:
            print(f"[{log['timestamp']}] {log['action']} - {log['resource_type']}")
        
        # 3. 按操作类型过滤审计日志
        print("\n3. 查询登录操作审计日志")
        login_audit_logs = await system_service.query_audit_logs(
            tenant_id="tenant_demo",
            action="LOGIN",
            limit=5
        )
        
        print(f"登录审计日志数量: {login_audit_logs['total']}")
        for log in login_audit_logs['audit_logs']:
            print(f"[{log['timestamp']}] {log['username']} 登录")
            print(f"  IP地址: {log['ip_address']}")
        
        # 4. 按时间范围查询审计日志
        print("\n4. 查询最近24小时审计日志")
        start_time = (datetime.utcnow() - timedelta(hours=24)).isoformat()
        end_time = datetime.utcnow().isoformat()
        
        recent_audit_logs = await system_service.query_audit_logs(
            tenant_id="tenant_demo",
            start_time=start_time,
            end_time=end_time,
            limit=10
        )
        
        print(f"最近24小时审计日志数量: {recent_audit_logs['total']}")
        
        # 统计操作类型
        action_stats = {}
        for log in recent_audit_logs['audit_logs']:
            action = log['action']
            action_stats[action] = action_stats.get(action, 0) + 1
        
        print("操作类型统计:")
        for action, count in action_stats.items():
            print(f"  {action}: {count} 次")
        
    except Exception as e:
        print(f"审计日志查询演示出错: {e}")
    finally:
        await session.close()


async def demo_system_config():
    """演示系统配置管理功能"""
    system_service, session = await create_system_service()
    
    try:
        print("\n=== 系统配置管理演示 ===")
        
        # 1. 设置系统配置
        print("\n1. 设置系统配置")
        config_result = await system_service.set_system_config(
            config_key="max_upload_size",
            config_value="100MB",
            config_type="string",
            description="最大文件上传大小限制",
            is_public=True,
            version="1.0",
            created_by="admin"
        )
        
        print(f"配置设置成功:")
        print(f"  配置键: {config_result['config_key']}")
        print(f"  配置值: {config_result['config_value']}")
        print(f"  描述: {config_result['description']}")
        print(f"  是否公开: {config_result['is_public']}")
        
        # 2. 获取系统配置
        print("\n2. 获取系统配置")
        try:
            get_config_result = await system_service.get_system_config("max_upload_size")
            print(f"获取配置成功:")
            print(f"  配置ID: {get_config_result['config_id']}")
            print(f"  配置键: {get_config_result['config_key']}")
            print(f"  配置值: {get_config_result['config_value']}")
            print(f"  配置类型: {get_config_result['config_type']}")
            print(f"  版本: {get_config_result['version']}")
        except Exception as e:
            print(f"获取配置失败: {e}")
        
        # 3. 更新系统配置
        print("\n3. 更新系统配置")
        update_config_result = await system_service.set_system_config(
            config_key="max_upload_size",
            config_value="200MB",
            config_type="string",
            description="更新的最大文件上传大小限制",
            is_public=True,
            version="1.1",
            created_by="admin"
        )
        
        print(f"配置更新成功:")
        print(f"  新配置值: {update_config_result['config_value']}")
        print(f"  新版本: {update_config_result['version']}")
        
    except Exception as e:
        print(f"系统配置管理演示出错: {e}")
    finally:
        await session.close()


async def demo_cache_management():
    """演示缓存管理功能"""
    system_service, session = await create_system_service()
    
    try:
        print("\n=== 缓存管理演示 ===")
        
        # 1. 获取缓存统计
        print("\n1. 获取缓存统计信息")
        cache_stats = await system_service.get_cache_statistics()
        
        print(f"缓存统计时间: {cache_stats['timestamp']}")
        print("缓存类型统计:")
        for cache_type in cache_stats['cache_types']:
            print(f"  {cache_type['type']}: {cache_type['key_count']} 个键, {cache_type['total_access']} 次访问")
        
        print("Redis信息:")
        redis_info = cache_stats['redis_info']
        for key, value in redis_info.items():
            print(f"  {key}: {value}")
        
        # 2. 按模式清除缓存
        print("\n2. 清除会话缓存")
        clear_result = await system_service.clear_cache(pattern="session:*")
        
        print(f"清除缓存结果:")
        print(f"  清除数量: {clear_result['cleared_count']}")
        print(f"  清除模式: {clear_result['pattern']}")
        print(f"  清除时间: {clear_result['cleared_at']}")
        
        # 3. 清除所有缓存（谨慎操作）
        print("\n3. 清除所有缓存（演示，实际使用需谨慎）")
        # clear_all_result = await system_service.clear_cache()
        # print(f"清除所有缓存: {clear_all_result['cleared_count'] == -1}")
        print("（跳过全部清除操作以保护数据）")
        
    except Exception as e:
        print(f"缓存管理演示出错: {e}")
    finally:
        await session.close()


async def main():
    """主函数"""
    print("系统服务使用示例")
    print("=" * 50)
    
    # 运行各个演示
    await demo_metrics_query()
    await demo_logs_query()
    await demo_audit_logs_query()
    await demo_system_config()
    await demo_cache_management()
    
    print("\n演示完成！")


if __name__ == "__main__":
    asyncio.run(main())
