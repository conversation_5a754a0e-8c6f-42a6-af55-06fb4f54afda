# 角色管理API模型示例

本文档展示角色管理相关API的请求和响应模型结构。

## 请求模型 (Request Models)

### 1. CreateRoleRequest - 创建角色请求
```python
class CreateRoleRequest(BaseModel):
    """创建角色请求模型"""
    role_name: str = Field(..., description="角色名称", min_length=1, max_length=100)
    role_code: str = Field(..., description="角色编码", min_length=1, max_length=50)
    description: Optional[str] = Field(None, description="角色描述", max_length=500)
    tenant_id: str = Field(..., description="租户ID")
    permission_ids: Optional[List[str]] = Field(default=[], description="权限ID列表")

# 请求示例
{
    "role_name": "系统管理员",
    "role_code": "ADMIN",
    "description": "系统管理员角色，拥有所有权限",
    "tenant_id": "tenant_550e8400-e29b-41d4-a716-446655440000",
    "permission_ids": [
        "perm_550e8400-e29b-41d4-a716-446655440001",
        "perm_550e8400-e29b-41d4-a716-446655440002"
    ]
}
```

### 2. ListRolesRequest - 查询角色列表请求
```python
class ListRolesRequest(BaseModel):
    """查询角色列表请求模型"""
    tenant_id: str = Field(..., description="租户ID")
    limit: int = Field(default=20, description="每页数量", ge=1, le=100)
    search: Optional[str] = Field(None, description="搜索关键词", max_length=100)
    status: Optional[str] = Field(None, description="角色状态", regex="^(active|inactive|deleted)$")

# 请求示例
{
    "tenant_id": "tenant_550e8400-e29b-41d4-a716-446655440000",
    "limit": 20,
    "search": "管理员",
    "status": "active"
}
```

### 3. GetRoleRequest - 获取角色详情请求
```python
class GetRoleRequest(BaseModel):
    """获取角色详情请求模型"""
    role_id: str = Field(..., description="角色ID")
    tenant_id: str = Field(..., description="租户ID")

# 请求示例
{
    "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
    "tenant_id": "tenant_550e8400-e29b-41d4-a716-446655440000"
}
```

### 4. UpdateRoleRequest - 更新角色请求
```python
class UpdateRoleRequest(BaseModel):
    """更新角色请求模型"""
    role_id: str = Field(..., description="角色ID")
    tenant_id: str = Field(..., description="租户ID")
    role_name: Optional[str] = Field(None, description="角色名称", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="角色描述", max_length=500)
    status: Optional[str] = Field(None, description="角色状态", regex="^(active|inactive)$")

# 请求示例
{
    "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
    "tenant_id": "tenant_550e8400-e29b-41d4-a716-446655440000",
    "role_name": "高级管理员",
    "description": "更新后的角色描述",
    "status": "active"
}
```

### 5. DeleteRoleRequest - 删除角色请求
```python
class DeleteRoleRequest(BaseModel):
    """删除角色请求模型"""
    role_id: str = Field(..., description="角色ID")
    tenant_id: str = Field(..., description="租户ID")
    force: bool = Field(default=False, description="是否强制删除")

# 请求示例
{
    "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
    "tenant_id": "tenant_550e8400-e29b-41d4-a716-446655440000",
    "force": true
}
```

### 6. AssignPermissionsRequest - 分配权限请求
```python
class AssignPermissionsRequest(BaseModel):
    """分配权限请求模型"""
    role_id: str = Field(..., description="角色ID")
    tenant_id: str = Field(..., description="租户ID")
    permission_ids: List[str] = Field(..., description="权限ID列表", min_items=1)

# 请求示例
{
    "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
    "tenant_id": "tenant_550e8400-e29b-41d4-a716-446655440000",
    "permission_ids": [
        "perm_550e8400-e29b-41d4-a716-446655440001",
        "perm_550e8400-e29b-41d4-a716-446655440002"
    ]
}
```

### 7. RemovePermissionsRequest - 移除权限请求
```python
class RemovePermissionsRequest(BaseModel):
    """移除权限请求模型"""
    role_id: str = Field(..., description="角色ID")
    tenant_id: str = Field(..., description="租户ID")
    permission_ids: List[str] = Field(..., description="权限ID列表", min_items=1)

# 请求示例
{
    "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
    "tenant_id": "tenant_550e8400-e29b-41d4-a716-446655440000",
    "permission_ids": [
        "perm_550e8400-e29b-41d4-a716-446655440001"
    ]
}
```

## 响应模型 (Response Models)

### 1. RoleResponseModel - 角色响应模型
```python
class RoleInfo(BaseModel):
    """角色信息模型"""
    role_id: str = Field(..., description="角色ID")
    role_name: str = Field(..., description="角色名称")
    role_code: str = Field(..., description="角色编码")
    description: Optional[str] = Field(None, description="角色描述")
    tenant_id: str = Field(..., description="租户ID")
    status: str = Field(..., description="角色状态")
    level: int = Field(..., description="角色层级")
    parent_role_id: Optional[str] = Field(None, description="父角色ID")
    max_users: int = Field(default=0, description="最大用户数")
    meta_data: Dict[str, Any] = Field(default={}, description="元数据")
    permissions: List[PermissionInfo] = Field(default=[], description="权限列表")
    user_count: int = Field(default=0, description="用户数量")
    created_at: str = Field(..., description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")

class RoleResponseModel(BaseResponseModel):
    """角色响应模型"""
    data: RoleInfo

# 响应示例
{
    "status": "success",
    "code": 200,
    "message": "操作成功",
    "data": {
        "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
        "role_name": "系统管理员",
        "role_code": "ADMIN",
        "description": "系统管理员角色",
        "tenant_id": "tenant_550e8400-e29b-41d4-a716-446655440000",
        "status": "active",
        "level": 1,
        "parent_role_id": null,
        "max_users": 0,
        "meta_data": {},
        "permissions": [
            {
                "permission_id": "perm_550e8400-e29b-41d4-a716-446655440001",
                "permission_name": "用户管理",
                "permission_code": "user:manage",
                "resource": "user",
                "action": "manage",
                "description": "管理用户的权限",
                "assigned_at": "2025-01-15T10:30:45.123456"
            }
        ],
        "user_count": 2,
        "created_at": "2025-01-15T10:30:45.123456",
        "updated_at": "2025-01-15T10:30:45.123456"
    }
}
```

### 2. RoleListResponseModel - 角色列表响应模型
```python
class RoleListData(BaseModel):
    """角色列表数据模型"""
    roles: List[RoleInfo] = Field(..., description="角色列表")
    total: int = Field(..., description="总数量")
    limit: int = Field(..., description="每页数量")

class RoleListResponseModel(BaseResponseModel):
    """角色列表响应模型"""
    data: RoleListData

# 响应示例
{
    "status": "success",
    "code": 200,
    "message": "查询成功",
    "data": {
        "roles": [
            {
                "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
                "role_name": "系统管理员",
                "role_code": "ADMIN",
                "description": "系统管理员角色",
                "level": 1,
                "status": "active",
                "user_count": 2,
                "permission_count": 15,
                "created_at": "2025-01-15T10:30:45.123456"
            }
        ],
        "total": 1,
        "limit": 20
    }
}
```

### 3. RoleOperationResponseModel - 角色操作响应模型
```python
class RoleOperationData(BaseModel):
    """角色操作数据模型"""
    success: bool = Field(..., description="操作是否成功")
    role_id: str = Field(..., description="角色ID")
    # 其他字段根据具体操作而定

class RoleOperationResponseModel(BaseResponseModel):
    """角色操作响应模型"""
    data: RoleOperationData

# 权限分配响应示例
{
    "status": "success",
    "code": 200,
    "message": "权限分配成功",
    "data": {
        "success": true,
        "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
        "assigned_permissions": [
            {
                "permission_id": "perm_550e8400-e29b-41d4-a716-446655440001",
                "permission_name": "用户管理",
                "permission_code": "user:manage",
                "assigned_at": "2025-01-15T10:30:45.123456"
            }
        ],
        "failed_permissions": [],
        "assigned_at": "2025-01-15T10:30:45.123456"
    }
}
```

## 通用字段说明

### 权限信息模型
```python
class PermissionInfo(BaseModel):
    """权限信息模型"""
    permission_id: str = Field(..., description="权限ID")
    permission_name: str = Field(..., description="权限名称")
    permission_code: str = Field(..., description="权限编码")
    resource: str = Field(..., description="资源类型")
    action: str = Field(..., description="操作类型")
    description: Optional[str] = Field(None, description="权限描述")
    assigned_at: Optional[str] = Field(None, description="分配时间")
```

### 状态枚举
- `active`: 激活状态
- `inactive`: 非激活状态  
- `deleted`: 已删除状态

### 时间格式
所有时间字段使用ISO 8601格式：`2025-01-15T10:30:45.123456`
