# TSIF Micro - Containers 模块框架设计文档

## 1. 框架整体概述

**核心目标与设计理念**  
Containers 模块是基于依赖注入（DI）模式的配置和基础设施管理框架，旨在实现组件间的松耦合、提高可测试性并统一管理应用的生命周期。框架采用声明式容器设计，通过统一的依赖注入容器管理配置、数据库连接、缓存等基础设施组件。

**解决的问题与适用场景**  
- 解决微服务中配置分散、连接管理复杂的问题
- 统一管理多种存储系统（Redis、MySQL、MongoDB、RabbitMQ）的连接生命周期
- 提供可扩展的基础设施组件注册和管理机制
- 适用于需要多存储支持、高可配置性的微服务架构

**主要技术栈与依赖**  
- **核心库**: dependency-injector (Python DI 框架)
- **配置管理**: pydantic-settings, python-dotenv
- **数据库**: asyncpg, aiomysql, pymongo, aio-pika
- **日志**: loguru
- **运行环境**: Python 3.12+, FastAPI

## 2. 模块划分与职责

### 2.1 核心容器模块

**ConfigContainer** (`config_container.py`)
- **功能职责**: 管理全局配置加载、日志初始化、配置项暴露
- **核心组件**: ConfigLoader(单例), logger(资源), 各种配置提供者
- **交互关系**: 为 InfraContainer 和业务服务提供配置依赖

**InfraContainer** (`infra_container.py`)  
- **功能职责**: 管理基础设施组件，包括数据库客户端、仓储层、装饰器等
- **核心组件**: ConnectionManager(连接管理), 各种客户端 Provider, Repository Factory
- **交互关系**: 依赖 ConfigContainer 的配置，为业务层提供基础设施服务

### 2.2 分层结构
```
ConfigContainer (配置层)
       ↓ (config dependency)
InfraContainer (基础设施层)  
       ↓ (infra dependency)
ServiceContainer (业务服务层)
       ↓ (service injection)
FastAPI Routes (接口层)
```

## 3. 核心组件与关键类

### 3.1 关键基础类
- **`DeclarativeContainer`**: dependency-injector 的声明式容器基类
- **`ConnectionManager`**: 统一的连接管理器，支持按优先级连接/断开
- **`BaseConnector`**: 所有存储连接器的抽象基类
- **`ConfigLoader`**: 配置加载器，支持多源配置合并

### 3.2 Provider 类型与职责
- **`Singleton`**: 全局唯一实例（如 ConfigLoader）
- **`Resource`**: 需要生命周期管理的资源（如数据库连接、日志器）
- **`Factory`**: 工厂模式提供者（如 Repository 工厂）
- **`Callable`**: 函数调用提供者（如配置获取器）

### 3.3 扩展点设计
- 通过 `populate_connectors()` 动态注册新的存储连接器
- 支持自定义 Repository 实现和装饰器
- 可插拔的配置加载策略

## 4. 框架运行流程

### 4.1 应用启动流程
```mermaid
graph TD
    A[应用启动] --> B[加载环境变量]
    B --> C[初始化 ConfigContainer]
    C --> D[加载配置文件]
    D --> E[初始化日志系统]
    E --> F[创建 InfraContainer]
    F --> G[注册连接器]
    G --> H[按优先级建立连接]
    H --> I[Wire 依赖注入]
    I --> J[启动 FastAPI 应用]
```

### 4.2 请求处理流程
```mermaid
graph TD
    A[接收请求] --> B[中间件处理]
    B --> C[路由匹配]
    C --> D[依赖注入解析]
    D --> E[获取基础设施服务]
    E --> F[执行业务逻辑]
    F --> G[返回响应]
```

### 4.3 生命周期管理
- **启动阶段**: `init_resources()` → 按优先级连接所有存储
- **运行阶段**: 通过依赖注入提供服务
- **关闭阶段**: `shutdown_resources()` → 按反向优先级关闭连接

## 5. 配置与可扩展性

### 5.1 配置管理策略
- **环境变量**: 通过 `.env` 文件和系统环境变量
- **JSON 配置文件**: 支持多配置文件合并（`config.json`）
- **代码配置**: 通过 `AppSettings` 类定义默认值
- **优先级**: 环境变量 > JSON 文件 > 代码默认值

### 5.2 可扩展性设计
**添加新存储类型**:
1. 实现 `BaseConnector` 子类
2. 在 `populate_connectors.py` 中注册连接器映射
3. 在 `InfraContainer` 中添加对应的 Provider

**添加新装饰器**:
1. 在 `domain_common/interface` 下实现装饰器
2. 在 `InfraContainer` 中注册 Factory
3. 通过 `@inject` 装饰器使用

**扩展配置项**:
1. 在相应的配置类中添加字段
2. 在 `ConfigContainer` 中暴露新的配置提供者

## 6. 依赖与外部集成

### 6.1 核心依赖
- **存储系统**: Redis, MySQL, PostgreSQL, MongoDB, RabbitMQ
- **配置系统**: JSON 文件, 环境变量, Pydantic 模型
- **日志系统**: Loguru 结构化日志
- **监控集成**: 支持健康检查、指标收集

### 6.2 外部系统交互方式
- **数据库**: 异步连接池 + Repository 模式
- **缓存**: Redis 装饰器 + Lua 脚本
- **消息队列**: RabbitMQ 异步消息处理
- **监控**: Prometheus 指标暴露 + 健康检查端点

## 7. 安全与容错

### 7.1 安全机制
- **配置安全**: 敏感信息通过环境变量注入，避免硬编码
- **连接安全**: 支持 SSL/TLS 连接配置
- **日志安全**: 自动过滤敏感字段（password, token 等）
- **依赖隔离**: 通过容器隔离，避免直接依赖

### 7.2 容错设计
- **连接重试**: 支持连接失败重试机制
- **优雅降级**: 连接失败时不影响应用启动（可配置）
- **资源清理**: 保证资源在异常情况下正确释放
- **错误传播**: 统一的异常处理和错误传播机制

## 8. 当前已知问题 & 优化方向

### 8.1 当前限制
- **循环依赖**: 复杂的服务间依赖可能导致循环依赖问题
- **内存占用**: 大量单例实例可能增加内存占用
- **配置复杂性**: 多层配置结构增加了配置管理复杂度
- **调试困难**: 依赖注入链路较长，调试追踪较困难

### 8.2 优化方向
- **异步优化**: 进一步优化异步资源初始化和清理
- **配置热重载**: 支持运行时配置热重载
- **更好的错误提示**: 改进依赖注入失败时的错误信息
- **性能监控**: 添加容器性能监控和资源使用统计

## 9. 示例用例

### 9.1 基础用法示例
```python
# 1. 初始化配置容器
from commonlib.core.containers.config_container import set_up_config_di
from commonlib.core.containers.infra_container import InfraContainer

# 配置容器初始化
config = set_up_config_di()

# 基础设施容器初始化
infra = InfraContainer(config=config)

# 2. 在 FastAPI 应用中使用
from fastapi import FastAPI
from dependency_injector.wiring import inject, Provide

app = FastAPI()

@app.get("/users")
@inject
async def get_users(
    redis_repo = Provide[InfraContainer.decorator_redis_repo]
):
    # 使用注入的 Redis 仓储
    cached_users = await redis_repo.get("users:list")
    return cached_users or []

# 3. Wire 依赖注入
infra.wire(modules=["your_module"])
```

### 9.2 自定义存储扩展示例
```python
# 1. 实现自定义连接器
from commonlib.storages.base import BaseConnector

class ClickHouseConnector(BaseConnector):
    async def start(self):
        # 实现 ClickHouse 连接逻辑
        pass
    
    async def stop(self, fource_stop=False):
        # 实现连接关闭逻辑
        pass

# 2. 注册到容器
# 在 populate_connectors.py 中添加
_CONNECTOR_MAPPING["clickhouse"] = "your_module.ClickHouseConnector"

# 3. 在 InfraContainer 中使用
clickhouse_client = providers.Callable(
    lambda infra: infra.get_connector("clickhouse"), 
    infra=connection_manager
)
```

---

**注意**: 该框架采用了现代 Python 异步编程模式和依赖注入设计模式，适合构建高性能、可扩展的微服务应用。在使用时请注意依赖注入的 wire 范围配置，确保正确的依赖解析。 